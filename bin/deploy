#!/usr/bin/env bash

set -o nounset
set -o errexit
set -o pipefail
set -e

BRANCH=$(git rev-parse --abbrev-ref HEAD)
REMOTE=${1:-}
echo $REMOTE
echo "*************************"
if [ -z "$REMOTE" ]; then
  echo "please set heroku remote argument in deploy command"
  exit
fi

heroku pg:backups capture DATABASE_URL --remote $REMOTE
heroku maintenance:on --remote $REMOTE
git push -f $REMOTE $BRANCH:master
heroku run rake db:migrate --remote $REMOTE
heroku restart --remote $REMOTE
heroku maintenance:off --remote $REMOTE