# frozen_string_literal: true

# Observer
# 10098955
# Canvas ID: 6671
# https://stridelearning2.instructure.com/users/6671

# Missing Student
# <PERSON><PERSON>tudent
# Canvas ID: 6433
# https://stridelearning2.instructure.com/users/6433

# Course Having Above Student & Observer
# https://test-learn0.qa.k12.com/courses/2607/users
# https://test-learn0.qa.k12.com/courses/2610/users
# https://test-learn0.qa.k12.com/courses/2612/users
# https://test-learn0.qa.k12.com/courses/2570/users
# https://test-learn0.qa.k12.com/courses/2564/users

switch_tenant('stridelearning2')

# Course.where(canvas_id: [ 2607, 2610, 2612, 2570, 2564 ])

User.find_by_canvas_id(6671)
student = User.find_by_canvas_id(6433)

observer = User.find_by_first_name(10_098_955)

UserShardAssociation.where(user: observer)
UserShardAssociation.where(user: student)

UserShardAssociation.where(user: observer).first.organization.switch_tenant do
  p Course.where(canvas_id: [ 2607, 2610, 2612, 2570, 2564 ]).pluck :name
end

switch_tenant('stridelearning') { p Enrollment.find_by(canvas_user_id: 276_460_000_000_006_671) }

switch_tenant('stridelearning')

switch_tenant('stridelearning2')
observer = User.find_by_first_name(10_098_955)
observer.related_organizations
QueryBuilder::ObservedUsers.new(observer:).records
QueryBuilder::ObservedUsers.new(observer:).records.last

observer.reload.against_shards do |u|
  u.active_observer_enrollments.to_a
end.flatten.last
observer.against_shards do |u|
  # p current_organization
  # p u
  # p u.enrollments
  # p u.active_enrollment_observed_users.to_a
  u.active_observer_enrollments.map(&:observed_user)
  # tz << u.active_enrollment_observed_users.to_a
end.flatten.uniq.count
# tz

User.class_eval do
  # Returns an array of organizations associated with the user through user_shard_associations
  # @return [Array<Organization>] A list of organizations linked to the user
  def related_organizations
    user_shard_associations.map(&:organization)
  end
end

# ######################################################

users = []

observer.against_shards do |obs|
  users += User
           .joins("INNER JOIN enrollments ON enrollments.canvas_associated_user_id = users.canvas_id")
           .joins("INNER JOIN courses ON enrollments.canvas_course_id = courses.canvas_id")
           .joins("INNER JOIN accounts ON courses.canvas_account_id = accounts.canvas_id")
           .where(enrollments: {
                    canvas_user_id: obs.canvas_id,
                    workflow_state: %w[active concluded]
                  })
           .where(accounts: { enabled: true })
           .distinct
           .order(:sortable_name)
           .to_a
end

PandaPal::Organization.first.switch_tenant { p z.learning_coaches }

switch_tenant 'stridelearning2'
users = User.includes(:active_learning_coaches).where(canvas_id: UserShardAssociation.pluck(:canvas_user_id))
users.select { |user| user.active_learning_coaches.any? }

users.select { |user| user.active_learning_coaches.any? && user.against_shards(&:courses).flatten.any? }

query_builder = QueryBuilder::StudentCourses.new(
  student: User.find_by(canvas_id: 1196),
  is_stride_admin: false,
  is_account_admin: false,
  is_teacher: false,
  current_user: current_user
)

courses = query_builder.records
courses.map(&:attributes) # This will give you an array of courses with the selected fields.

# ========================================

switch_tenant('stridelearning2')
observer = User.find_by_first_name(10_098_955)
observer.related_organizations
users = QueryBuilder::ObservedUsers.new(observer:).records
user = QueryBuilder::ObservedUsers.new(observer:).records.first
QueryBuilder::ObservedUsers.new(observer:).records.first

user.against_shards do |shard_user|
  shard_user = User.find_by(canvas_id: shard_user.canvas_id)
  shard_user.active_learning_coaches.select(
    <<~SQL.squish
      users.canvas_id % #{PandaPal::Organization::SHARD_OFFSET} AS canvas_id,
      users.email,
      users.sortable_name,
      users.contact_information,
      users.contact_information2
    SQL
  )
end.flatten

switch_tenant('stridelearning2')
@user = User.find_by(canvas_id: 6433)

switch_tenant('stridelearning')
User.find_by_canvas_id(276_460_000_000_006_433)
