# Learning Coach Attendance

### Customer: Stride, Inc

### [SSD Phase 1](https://docs.google.com/document/d/1iCK2I8QZkd-t39izWs5rq-tZqDAxcWQalqY0DgRhhwk/edit?usp=sharing)
### [SSD Phase 2](https://docs.google.com/document/d/17FCB_aeeJM7BSB0mTQX5JW_HqoJRUpJ4L_8yREuOtAk/edit?tab=t.0)

### Architecture

#### Sharding
This application is aware of and mimics <PERSON>vas' sharding approach. It also leverages the multi-DB features added to PandaPal 5.12. See PandaPal README for instructions on configuring.

This app is also aware of Canvas' sharding approach. The `User` model has methods for querying across shards and tracking if a user belongs to multiple shards (See `RefreshCrossShardUsersJob`).

In cloudgate envirionments yml file, you will have defined several databases. On your first deploy, you will need to manually create the dbs by doing something like:
`DATABASE_URL="$SHARD_DB_ALPHA_URL" rails db:create` in a bash console. I used the worker box to do this for each shard.

##### Terminology
- "shard": A string key/identifier (added as a column on `Organization`). Maps to which DB instance this `Organization`'s data should be stored on.
- `PandaPal::Organization.name`: The key used when calling `switch_tenant` and the schema name - both as normal.
- "canvas_shard_id": The shard ID that Canvas would use, without trailing `0`s
- "Shadow Record": When a User exists in multiple shards, Canvas creates shadow records in each shard that's not the primary. CanvasSync will pulls these records as normal, but this app will need to recognize when a record is a shadow (its `canvas_id` will be sharded).

### Application Setup

1. Ensure that all packages are installed:
   - Run `yarn` to install JavaScript dependencies.
   - Run `bundle` to install Ruby dependencies.

2. Create and migrate the database:
   ```
   bundle exec rake db:create db:migrate
   ```

   If there are pending migrations, run:
   ```
   bundle exec rake db:migrate
   ```

3. Build the JavaScript files:
   ```
   bin/shakapacker-dev-server
   ```

   This will keep Shakapacker running and watching for changes to source files.

4. Generate an API token:
   - Go to `http://localhost:3000/profile/settings` and generate a 'New Access Token'.
   - Use this token in the Canvas settings as the `api_token`.

### PandaPal Gem

The PandaPal gem is used for multi-tenancy support and easy LTI configuration. PandaPal provides an LTI config endpoint at `/lti/v1p3/config` and is configured for multiple launch entry-points (see `./config/initializers/lti.rb`).

- A database must be created before proceeding with the following steps.
- For each client with access to the LTI, create a `PandaPal::Organization` object in the database using rails console.
- Locally: Run `rails c <environment>`.
- On Heroku: Run `heroku run rails c`.


### LTI Configuration

* Copy .env.example to .env.development, and fill out specified variables
   ```
   cp .env.example .env.development
   ```

1. **Run the Application**
   Start the Rails server by running:
   ```
   rails server -p 4000
   ```
   ```
   sidekiq
   ```

2. **Create an Organization and connect to Canvas**
   ```
   rails panda_pal:org:new
   ```

3. **Sync Data**
   Run `CanvasSyncStarterJob.perform_later` in Rails console to sync data.

4. **Access the Application**

   You can now launch the application from any launch point (Account/User/Course).

### Testing

1. **Run Test Suite**

   Run the test suite by running:
   ```
   rspec
   ```

### API Token

1. **Generate an API Token for recording Attendance by API**

- Run following script in rails console (The raw token will be only available once. May sure to store it safely):
   ```
   User.find_by_email('EMAIL').api_keys.create(name: 'API_KEY_NAME').raw_token
   ```

- Use this token with the Collection in `docs` folder.

### Other Services

The LTI depends on and requires API token for:

- Canvas API
- Hosted Data URL(s) for Pushing data to hosted servers/destinations (Optional)
- SFTP for Customer SIS Data Import (Optional)

- For Pushing DATA to Snowflake/Hosted data, add url to Organization setting in data_shipper_destinations

### SFTP Endpoints Configuration to Import ZIP files

To enable data import from remote SFTP servers, define the `sftp_endpoints` configuration as an array of endpoint objects for PandaPal::Organization settings.

```ruby
sftp_endpoints: [
  {
    host: "sftp.example.com",
    user: "username",
    password: "password",
    file_path: "/remote/zip/files",
  }
],
instance_names: ["iowak12", "oregonk12"] # optional
```

Each endpoint should include the following keys:
`host` – SFTP server hostname or IP.
`user` – Username for authentication.
`password` – Password for authentication.
`file_path` – Remote directory path where .zip files are stored.
`instance_names` (optional) – Array of substrings. Only .zip files that include any of these substrings in their filenames will be downloaded.

If instance_names is not provided or is empty, all .zip files in the specified directory will be downloaded.

### SFTP Endpoints Configuration to Import Blackout Dates & Learning Coach Information

To enable CSV imports from remote SFTP servers, define the `sftp` configuration as an Hash object for PandaPal::Organization settings.

Each endpoint should include the following keys:

```ruby
sftp: {
  {
    host: "sftp.example.com",
    user: "username",
    password: "password",
    file_path: "/remote/csv/files"
  }
}
```

### Canvas Reports

- Ensure **Canvas Reports** is enabled for Proserv Provisioning.

### Known Issues

- If roles are not synced, run following script in rails console:
   ```
   PandaPal::Organization.find_each do |org|
     switch_tenant(org.name) do
      CanvasSync::Jobs::SyncRolesJob.perform_later({})
     end
   end
   ```
