source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.3.3'

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem 'rails', '~> 7.0.8', '>= *******'

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem 'sprockets-rails'

# Use postgresql as the database for Active Record
gem 'pg', '~> 1.1'

# Use the Puma web server [https://github.com/puma/puma]
gem 'puma', '~> 5.0'

# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem 'jbuilder'

# Use Redis adapter to run Action Cable in production
# gem "redis", "~> 4.0"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: %i[ mingw mswin x64_mingw jruby ]

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', require: false

# Use Sass to process CSS
# gem "sassc-rails"

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

# Canvas
gem 'panda_pal', '5.13.2'
gem 'bearcat', '~> 1.5'
gem 'cancancan', '~> 3.6'
gem 'canvas_sync', '~> 0.22.16'
gem 'paul_bunyan'

# Background Jobs
gem 'ros-apartment-sidekiq', '~> 1.2', require: 'apartment-sidekiq'
gem 'sidekiq', '~> 7.3'
gem 'sidekiq-scheduler', '~> 5.0'

# sentry
gem 'sentry-rails'
gem 'sentry-ruby'
gem 'sentry-sidekiq'
gem 'sidekiq-failures'

# Manage Javascript Modules
gem 'shakapacker', '~> 8.0'
# Integrate React.js with Rails views and controllers, the asset pipeline, or webpacker.
gem 'react-rails', '~> 3.2'

# Code Linting
gem 'rubocop', '~> 1.67', require: false

# Debugging
gem 'pry-rails', '~> 0.3.11'

gem 'gergich', '~> 2.2', require: false

# SFTP
gem 'net-sftp', '~> 4.0'
gem 'ed25519', '~> 1.3'
gem 'bcrypt_pbkdf', '~> 1.1'

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem 'debug', platforms: %i[ mri mingw x64_mingw ]

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem 'rubocop-rails-omakase', require: false

  # Load environment variables from .env into ENV in development.
  gem 'dotenv', '~> 3.1'

  # Testing
  gem 'rspec-rails', '~> 7.0'
  gem 'factory_bot_rails', '~> 6.4'
  gem 'faker', '~> 3.4'
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem 'web-console'

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
end

group :test do
  # Test Suite
  gem 'shoulda-matchers', '~> 6.4'
end

gem 'mutex_m', '~> 0.2.0'
gem 'ostruct', '~> 0.6.1'
gem 'drb', '~> 2.2'

gem 'aws-sdk-s3', '~> 1.175', require: false

# Faraday extra dependency
gem 'faraday-follow_redirects', '~> 0.3.0'

# Cloudgate
gem 'health_check', '~> 3.1'

# InstDataShipper - Facilitate easy upload of LTI datasets to Instructure Hosted Data
gem 'inst_data_shipper', '~> 0.2.6'
