{"info": {"_postman_id": "96b0c31e-56a0-48ac-a76a-8c1a9ef2887b", "name": "Stride Learning Coach Attendance", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "33767342"}, "item": [{"name": "Attendance API", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "course_sis_id", "value": "Js-Override-Test-Course", "description": "The course, designated by Course SIS ID, to which the student’s attendance values will be passed.\n", "type": "text"}, {"key": "date", "value": "2025-01-21", "description": "The date, in the format ’YYYY-MM-DD’, to which the attendance value will be applied for the provided course.", "type": "text"}, {"key": "minutes", "value": "121", "description": "Integer | The value in minutes to be applied for the student, for the provided course, for the provided date", "type": "text"}]}, "url": {"raw": "{{api_url}}/organizations/:organization_id/api/v1/:student_id/attendance", "host": ["{{api_url}}"], "path": ["organizations", ":organization_id", "api", "v1", ":student_id", "attendance"], "variable": [{"key": "organization_id", "value": "1"}, {"key": "student_id", "value": "109678"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "api_url", "value": "https://stride-learning-coach-attendance-qa.inseng.net", "type": "string"}, {"key": "token", "value": "JE3EcSEWdZDcfbkNFypa9jeFo853h8", "type": "string"}]}