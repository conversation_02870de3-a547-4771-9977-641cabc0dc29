pipeline {
    agent { label 'docker' }

    environment {
        COMPOSE_PROJECT_NAME = "${env.JOB_NAME}-${env.BUILD_ID}".replace("/","_")
        COMPOSE_FILE="docker-compose.test.yml"
        TEST_IMAGE_NAME = "stride-learning-coach-attendance"
        PUBLISHED_IMAGE_TAG = "starlord.inscloudgate.net/jenkins/stride-learning-coach-attendance:${GERRIT_PATCHSET_REVISION}"
        CHANGE_OWNER = "${GERRIT_CHANGE_OWNER_EMAIL.split("@")[0]}"
    }

    stages {
        stage("Build Image") {
            steps {
                dockerCacheLoad(image: env.TEST_IMAGE_NAME)
                timeout(time: 30, unit: 'MINUTES') {
                    sh """docker build --pull --tag "$TEST_IMAGE_NAME" --tag "$PUBLISHED_IMAGE_TAG"  -f Dockerfile ."""
                }
            }
        }
        stage("Test") {
            steps {
                sh "./build.sh"
            }
        }
        stage("Gerrit Push Publish") {
            parallel {
                stage("Production image") {
                    steps {
                        timeout(time: 30, unit: 'MINUTES') {
                            sh """docker push $PUBLISHED_IMAGE_TAG"""
                        }
                    }
                }
                stage("Docker build cache") {
                    steps {
                        timeout(time: 5, unit: 'MINUTES') {
                            dockerCacheStore(image: env.TEST_IMAGE_NAME)
                        }
                    }
                }
            }
        }
    }

    post {
        cleanup { // Always runs after all other post conditions
            sh 'docker-compose down --volumes --remove-orphans --rmi all'
        }
        failure{
            script {
                slackSend failOnError: true, channel: '#customdev-builds', color: 'danger', message: "@${CHANGE_OWNER} the stride-learning-coach-attendance gerrit-push <${env.BUILD_URL}|build> for your recently pushed patch failed!"
            }
        }
        success{
            script {
                slackSend failOnError: true, channel: '#customdev-builds', color: 'good', message: "stride-learning-coach-attendance gerrit-push <${env.BUILD_URL}|build> pushed a new images!\n$PUBLISHED_IMAGE_TAG"
            }
        }
    }
}
