{"packageManager": "yarn@1.22.22", "name": "app", "engines": {"node": "22.x"}, "private": true, "version": "0.1.0", "babel": {"presets": ["./node_modules/shakapacker/package/babel/preset.js", "@babel/preset-react"]}, "browserslist": ["defaults"], "dependencies": {"@babel/core": "^7.25.8", "@babel/plugin-transform-runtime": "^7.25.7", "@babel/preset-env": "^7.25.8", "@babel/runtime": "^7.25.7", "@inst_proserv/toolkit": "^0.2.11", "@instructure/debounce": "^10.6.1", "@instructure/ui": "^10.6.1", "@instructure/ui-alerts": "^10.6.1", "@instructure/ui-breadcrumb": "^10.6.1", "@instructure/ui-checkbox": "^10.6.1", "@instructure/ui-date-input": "^10.6.1", "@instructure/ui-drilldown": "^10.6.1", "@instructure/ui-flex": "^10.6.1", "@instructure/ui-heading": "^10.6.1", "@instructure/ui-icons": "^10.6.1", "@instructure/ui-modal": "^10.6.1", "@instructure/ui-overlays": "^10.6.1", "@instructure/ui-pagination": "^10.6.1", "@instructure/ui-radio-input": "^10.6.1", "@instructure/ui-simple-select": "^10.6.1", "@instructure/ui-spinner": "^10.6.1", "@instructure/ui-table": "^10.6.1", "@instructure/ui-tabs": "^10.6.1", "@instructure/ui-text": "^10.6.1", "@instructure/ui-text-area": "^10.6.1", "@instructure/ui-text-input": "^10.6.1", "@instructure/ui-tooltip": "^10.6.1", "@instructure/ui-tray": "^10.6.1", "@instructure/ui-view": "^10.6.1", "@types/babel__core": "^7.20.5", "@types/webpack": "^5.28.5", "axios": "^1.7.7", "babel-loader": "^8.4.1", "compression-webpack-plugin": "^9.2.0", "moment": "^2.30.1", "react_ujs": "^3.2.1", "sass": "^1.80.6", "sass-loader": "^16.0.3", "shakapacker": "8.0.2", "terser-webpack-plugin": "^5.3.10", "webpack": "^5.95.0", "webpack-assets-manifest": "^5.2.1", "webpack-cli": "^4.10.0", "webpack-merge": "^5.10.0"}, "devDependencies": {"prettier": "3.3.3", "webpack-dev-server": "^4.15.2"}}