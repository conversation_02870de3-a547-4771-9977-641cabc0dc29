# Omakase Ruby styling for Rails
inherit_gem: { rubocop-rails-omakase: rubocop.yml }

# Overwrite or add rules to create your own house style
#
# # Use `[a, [b, c]]` not `[ a, [ b, c ] ]`

Style/ClassAndModuleChildren:
  EnforcedStyle: compact

AllCops:
  NewCops: enable
  Exclude:
    - 'db/schema.rb'
    - 'vendor/**/*'
    - 'config/routes/**/*'
    - 'config/locales/**/*'
    - 'config/initializers/**/*'
    - 'config/application.rb'

Layout/LineLength:
  Max: 120

Style/StringLiterals:
  EnforcedStyle: single_quotes
  Include:
    - 'app/**/*'
    - 'config/**/*'
    - 'lib/**/*'
    - 'spec/**/*'
    - 'Gemfile'
