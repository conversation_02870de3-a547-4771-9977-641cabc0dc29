require 'rails_helper'

RSpec.describe UpdateCourseStartEndDatesJob, type: :job do
  let(:term_start) { Date.new(2024, 1, 1) }
  let(:term_end) { Date.new(2024, 12, 31) }
  let!(:term) { create(:term, start_at: term_start, end_at: term_end) }

  let!(:course_with_no_dates) { create(:course, resolved_start_date: nil, resolved_end_date: nil, term: term) }
  let!(:course_with_start_date) {
    create(:course, resolved_start_date: Date.new(2024, 3, 1), resolved_end_date: nil, term: term)
  }
  let!(:course_with_dates) {
    create(:course, resolved_start_date: Date.new(2024, 2, 1), resolved_end_date: Date.new(2024, 11, 30), term: term)
  }

  describe '#perform' do
    context 'when force_update is false' do
      it 'only updates courses with missing start_date or end_date' do
        expect {
          described_class.perform_now(force_update: false)
        }.to change { course_with_no_dates.reload.resolved_start_date }.from(nil).to(term_start)
          .and change { course_with_no_dates.reload.resolved_end_date }.from(nil).to(term_end)
          .and change { course_with_start_date.reload.resolved_end_date }.from(nil).to(term_end)

        # Ensure course with both dates set is not changed
        expect(course_with_dates.reload.resolved_start_date).to eq(Date.new(2024, 2, 1))
        expect(course_with_dates.reload.resolved_end_date).to eq(Date.new(2024, 11, 30))
      end
    end

    context 'when force_update is true' do
      it 'updates all courses regardless of existing dates' do
        expect {
          described_class.perform_now(force_update: true)
        }.to change { course_with_no_dates.reload.resolved_start_date }.from(nil).to(term_start)
          .and change { course_with_no_dates.reload.resolved_end_date }.from(nil).to(term_end)
          .and change { course_with_start_date.reload.resolved_end_date }.from(nil).to(term_end)
          .and change { course_with_dates.reload.resolved_start_date }.from(Date.new(2024, 2, 1)).to(term_start)
          .and change { course_with_dates.reload.resolved_end_date }.from(Date.new(2024, 11, 30)).to(term_end)
      end
    end
  end
end
