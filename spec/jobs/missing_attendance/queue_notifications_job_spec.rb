# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MissingAttendance::QueueNotificationsJob, type: :job do
  describe '#perform' do
    let(:account) { create(:account) }
    let(:teacher) { create(:user) }
    let(:observer) { create(:user) }
    let(:student) { create(:user) }
    let(:course) { create(:course, account: account) }
    let(:attendance_date) { Date.current }

    before do
      allow_any_instance_of(MissingAttendance::QueueNotificationsJob).to receive(:date).and_return(attendance_date)

      # Student Enrollments
      create :enrollment, :student, course:, user: student
      # Observer Enrollments
      create :enrollment, :observer, course:, user: observer
      create :enrollment, :observer, course:, user: observer, observed_user: student
      # Teacher Enrollments
      create :enrollment, :teacher, course:, user: teacher

      # Mock missing attendance
      create(:attendance, canvas_user_id: student.canvas_id, canvas_course_id: course.canvas_id,
                          attendance_date: attendance_date, time_in_minutes: nil)
    end

    context 'when attendance recording is skipped' do
      before do
        allow_any_instance_of(MissingAttendance::QueueNotificationsJob).to receive(:skip_attendance_recording?)
          .and_return(false)
      end
      it 'queues notifications for both teachers and observers when students have missing attendance' do
        expect(MissingAttendance::ProcessNotificationsJob).to receive(:perform_later).twice

        described_class.perform_now
      end
    end

    context 'when attendance recording is skipped' do
      before do
        allow_any_instance_of(MissingAttendance::QueueNotificationsJob).to receive(:skip_attendance_recording?)
          .and_return(true)
      end

      it 'does not queue any notifications' do
        expect(MissingAttendance::ProcessNotificationsJob).not_to receive(:perform_later)

        described_class.perform_now
      end
    end
  end
end
