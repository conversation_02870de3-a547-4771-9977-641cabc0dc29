# frozen_string_literal: true

require 'rails_helper'

RSpec.describe MissingAttendance::ProcessNotificationsJob, type: :job do
  describe '#perform' do
    let(:student) { create(:user) }
    let(:recipient) { create(:user) }
    let(:date) { Date.current }
    let(:canvas_sync_client) { double('CanvasSyncClient') }

    before do
      allow_any_instance_of(described_class).to receive(:canvas_sync_client).and_return(canvas_sync_client)
      allow(canvas_sync_client).to receive(:post)
    end

    context 'when inputs are valid' do
      it 'sends a notification with correct parameters' do
        expected_body_params = {
          subject: 'Attendance Not Recorded',
          body: 'This is an automated message to notify you that attendance for ' \
                "#{student.name}\nis not recorded on " \
                "#{date.strftime('%A, %B %d, %Y')}.",
          recipients: [ recipient.canvas_id ],
          force_new: true
        }

        expect(canvas_sync_client).to receive(:post).with('/api/v1/conversations', expected_body_params)

        described_class.perform_now(recipient.canvas_id, student.canvas_id, date)
      end
    end

    context 'when inputs are invalid' do
      it 'does not send a notification if recipient_id is missing' do
        expect(canvas_sync_client).not_to receive(:post)

        described_class.perform_now(nil, student.canvas_id, date)
      end

      it 'does not send a notification if student_id is missing' do
        expect(canvas_sync_client).not_to receive(:post)

        described_class.perform_now(recipient.canvas_id, nil, date)
      end

      it 'does not send a notification if date is missing' do
        expect(canvas_sync_client).not_to receive(:post)

        described_class.perform_now(recipient.canvas_id, student.canvas_id, nil)
      end
    end

    context 'when the user is not found' do
      it 'raises an ActiveRecord::RecordNotFound error' do
        expect { described_class.perform_now(recipient.canvas_id, 'nonexistent_canvas_id', date) }
          .to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end
end
