require 'rails_helper'

RSpec.describe MissingAttendance::RecordJob, type: :job do
  let(:date) { Date.yesterday }
  let(:user) { create(:user) }
  let(:student_role) { create(:role, :student) }
  let(:account) { create(:account, enabled: true) }
  let(:course) {
    create(:course, account: account, resolved_start_date: 2.days.ago, resolved_end_date: 2.days.from_now)
  }
  let!(:enrollment) { create(:enrollment, :student, role: student_role, course: course, user: user) }

  before do
    allow(Date).to receive(:yesterday).and_return(date)
    allow_any_instance_of(MissingAttendance::RecordJob).to receive(:skip_attendance_recording?).and_return(false)
  end

  describe '#perform' do
    context 'when date is a weekend or holiday' do
      it 'does not perform attendance recording' do
        allow_any_instance_of(MissingAttendance::RecordJob).to receive(:skip_attendance_recording?).and_return(true)

        expect_any_instance_of(MissingAttendance::RecordJob).not_to receive(:record_attendance_for_account)
        described_class.perform_now
      end
    end

    context 'when date is neither a weekend nor a holiday' do
      it 'records attendance for active accounts and courses' do
        expect { described_class.perform_now }.to change { Attendance.count }.by(1)

        attendance = Attendance.last
        expect(attendance.canvas_user_id).to eq(enrollment.canvas_user_id)
        expect(attendance.attendance_date).to eq(date)
        expect(attendance.course).to eq(course)
      end
    end

    context 'when attendance record already exists' do
      it 'does not create a duplicate attendance record' do
        Attendance.create!(canvas_user_id: enrollment.canvas_user_id, attendance_date: date, course: course)

        expect { described_class.perform_now }.not_to change { Attendance.count }
      end
    end
  end
end
