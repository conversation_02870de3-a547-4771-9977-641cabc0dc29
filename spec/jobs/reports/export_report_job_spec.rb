require 'rails_helper'

RSpec.describe Reports::ExportReportJob, type: :job do
  let(:report) { create(:report) }
  let(:job) { described_class.new }

  before do
    allow(job).to receive(:report).and_return(report)
    allow(job).to receive(:write_to_csv)
  end

  describe '#generate_report' do
    let(:attendance_date) { Date.current }
    let!(:course) { create(:course) }
    let!(:student) { create(:user, sis_id: 'SIS123', grade_level: '5th Grade') }
    let!(:learning_coach) { create(:user) }
    let!(:attendance) do
      create(
        :attendance,
        user: student,
        time_in_minutes: nil,
        attendance_date:,
        course:
      )
    end

    before do
      student.enrollments << create(:enrollment, :student, course:)
      learning_coach.enrollments << create(:enrollment, :observer, course:)
      learning_coach.enrollments << create(:enrollment, :observer, course:, observed_user: student)
    end

    it 'generates the correct CSV rows' do
      expect(job).to receive(:write_to_csv).with(
        'Missing_Attendance_Data_Report',
        described_class::HEADERS,
        [ {
          'User Name' => student.sortable_name,
          'User SIS ID' => student.sis_id,
          'User\'s Grade Level' => student.grade_level,
          'User Learning Coach' => learning_coach.sortable_name,
          'Attendance Date' => attendance_date
        } ]
      )

      job.generate_report
    end
  end
end
