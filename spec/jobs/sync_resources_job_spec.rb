require 'rails_helper'

RSpec.describe SyncResourcesJob, type: :job do
  include ActiveJob::TestHelper

  let(:sftp_client) { instance_double(SftpClient) }
  let(:mailer) { instance_double(ActionMailer::MessageDelivery) }
  let(:processor) do
    instance_double(CsvProcessor::UserInformationImporter, success?: success, rows_with_errors: rows_with_errors)
  end
  let(:success) { true }
  let(:rows_with_errors) { [] }
  let(:file_name) { 'users.csv' }
  let(:file_path) { "/tmp/#{file_name}" }

  before do
    stub_const('SyncResourcesJob::FILES', { users: 'users.csv' })

    allow(SftpClient).to receive(:new).and_return(sftp_client)
    allow(sftp_client).to receive(:download_file).with(file_name)
    allow(CsvProcessor::UserInformationImporter).to receive(:call).and_return(processor)
    allow(SyncResourcesMailer).to receive(:ftp_import_failed).and_return(mailer)
    allow(mailer).to receive(:deliver_later)
  end

  describe '#perform' do
    context 'when processing is successful' do
      it 'logs success and does not send an email' do
        expect(CsvProcessor::UserInformationImporter).to receive(:call)

        perform_enqueued_jobs { described_class.perform_now }

        expect(SyncResourcesMailer).not_to have_received(:ftp_import_failed)
      end
    end

    context 'when processing fails' do
      let(:success) { false }
      let(:rows_with_errors) { [ 2, 5, 8 ] }

      it 'logs error and sends an email' do
        expect(Rails.logger).to receive(:error).with('Error processing file users.csv: [2, 5, 8]').ordered
        expect(SyncResourcesMailer).to receive(:ftp_import_failed).with('users.csv', [ 2, 5, 8 ]).ordered
        expect(mailer).to receive(:deliver_later)

        perform_enqueued_jobs { described_class.perform_now }
      end
    end
  end
end
