# frozen_string_literal: true

require 'rails_helper'

RSpec.describe HostedDataPushJob, type: :job do
  include ActiveJob::TestHelper

  let(:organization) { current_organization }
  let(:settings) { { data_shipper_destinations: [ 's3://bucket/path' ] } }

  before do
    organization.update(settings: current_organization.settings.merge(settings))
  end

  describe '#perform' do
    context 'when destinations are present' do
      it 'calls Dumper.perform_dump with destinations' do
        expect(InstDataShipper::Dumper).to receive(:perform_dump).with(settings[:data_shipper_destinations])
        described_class.perform_now
      end
    end

    context 'when destinations are blank' do
      let(:settings) { { data_shipper_destinations: [] } }

      it 'does not call Dumper.perform_dump' do
        expect(InstDataShipper::Dumper).not_to receive(:perform_dump)
        described_class.new.perform
      end
    end
  end

  it 'enqueues the job' do
    expect { described_class.perform_later }.to have_enqueued_job(HostedDataPushJob).on_queue('default')
  end
end
