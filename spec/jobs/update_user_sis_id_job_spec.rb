# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UpdateUserSisIdJob, type: :job do
  describe '#perform' do
    let!(:user_with_sis_id) { create(:user, sis_id: '12345') }
    let!(:user_without_sis_id) { create(:user, :with_pseudonym, sis_id: nil) }

    context 'when force_update is false' do
      it 'only updates users with a nil sis_id and assigns the sis_id from the primary pseudonym' do
        expect(user_without_sis_id.sis_id).to be_nil

        expect do
          UpdateUserSisIdJob.perform_now(force_update: false)
        end.to change { user_without_sis_id.reload.sis_id }.from(nil).to(user_without_sis_id.primary_pseudonym.sis_id)

        expect(user_with_sis_id.reload.sis_id).to eq('12345')
      end
    end

    context 'when force_update is true' do
      it 'updates all users regardless of whether they have a sis_id or not' do
        user_with_sis_id.update(sis_id: nil)

        create(:pseudonym, user: user_with_sis_id)

        expect do
          UpdateUserSisIdJob.perform_now(force_update: true)
        end.to change { user_without_sis_id.reload.sis_id }.from(nil).to(user_without_sis_id.primary_pseudonym.sis_id)
           .and change { user_with_sis_id.reload.sis_id }.from(nil).to(user_with_sis_id.primary_pseudonym.sis_id)
      end
    end

    context 'when no pseudonym exists for the user' do
      it 'does not update sis_id if there is no primary pseudonym' do
        user_without_pseudonym = create(:user, sis_id: nil)
        expect do
           UpdateUserSisIdJob.perform_now(force_update: false)
        end.not_to change { user_without_pseudonym.reload.sis_id }
      end
    end
  end
end
