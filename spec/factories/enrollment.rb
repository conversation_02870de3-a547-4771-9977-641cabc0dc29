# frozen_string_literal: true

FactoryBot.define do
  factory :enrollment do
    sequence(:canvas_id) { |n| n }

    association :user
    association :course
    association :role

    course_sis_id { course.sis_id }

    base_role_type { 'TeacherEnrollment' }

    user_sis_id { user.sis_id }
    workflow_state { 'active' }

    trait(:student) do
      base_role_type { 'StudentEnrollment' }
    end

    trait(:observer) do
      base_role_type { 'ObserverEnrollment' }
    end

    trait(:teacher) do
      base_role_type { 'TeacherEnrollment' }
    end
  end
end
