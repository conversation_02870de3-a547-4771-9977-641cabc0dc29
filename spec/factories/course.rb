# frozen_string_literal: true

FactoryBot.define do
  factory :course do
    sequence(:canvas_id) { |n| n }

    association :account
    association :term

    course_code { Faker::Educator.course_name }
    name { Faker::Educator.subject }
    sis_id { Faker::Code.asin }
    workflow_state { 'active' }

    resolved_start_date { Faker::Date.between(from: 2.month.ago, to: Date.current) }
    resolved_end_date { Faker::Date.between(from: Date.current, to: 2.month.from_now) }
  end
end
