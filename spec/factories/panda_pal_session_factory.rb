FactoryBot.define do
  factory :session, class: 'PandaPal::Session' do
    session_key { 'PandaPal' }
    panda_pal_organization_id { 1 }

    transient do
      organization { nil }
      launch_context { nil }
      launch_course { launch_context.is_a?(Course) ? launch_context : nil }
      launch_account { launch_context.is_a?(Account) ? launch_context : launch_context&.account }
      user { nil }
      roles { [ 'Instructor' ] }
      permissions { nil }
      launch_params { {} }
    end

    data do
      hash = {
        organization_key: organization&.key || 'key',
        launch_params: {
          'email' => '<EMAIL>',
          "https://purl.imsglobal.org/spec/lti/claim/custom": {
            'canvas_role' => roles,
            'canvas_user_id' => user&.canvas_id,
            'canvas_course_id' => launch_course&.canvas_id,
            'canvas_account_id' => launch_account&.canvas_id
          }
        }.merge!(launch_params)
      }

      hash
    end

    factory :admin_session do
      transient do
        roles { 'Administrator,Account Admin' }
      end

      after :build do |ses|
        ses.data[:launch_params][:ext_roles] = 'urn:lti:sysrole:ims/lis/SysAdmin'
      end
    end
  end
end
