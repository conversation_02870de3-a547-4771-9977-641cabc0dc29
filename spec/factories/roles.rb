# frozen_string_literal: true

FactoryBot.define do
  factory :role do
    sequence(:canvas_id) { |n| n }

    label { 'Teacher' }
    base_role_type { 'TeacherEnrollment' }
    workflow_state { 'built_in' }

    trait :student do
      label { 'Student' }
      base_role_type { 'StudentEnrollment' }
      workflow_state { 'built_in' }
    end

    trait :account_admin do
      label { 'Account admin' }
      base_role_type { 'AccountMembership' }
      workflow_state { 'built_in' }
    end

    trait :observer do
      label { 'Observer' }
      base_role_type { 'ObserverEnrollment' }
      workflow_state { 'built_in' }
    end
  end
end
