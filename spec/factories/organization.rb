FactoryBot.define do
  factory :organization, class: PandaPal::Organization do
    name { 'test' }
    key { SecureRandom.hex(16) }
    secret { SecureRandom.hex(16) }
    canvas_account_id { 1 }
    salesforce_id { Faker::Number.number(digits: 8) }
    settings do
      {
        canvas: {
          base_url: 'http://localhost:3000/',
          api_token: 'test-token'
        },
        # TODO: Remove Legacy SFTP Importer
        sftp: {
          file_path: 'file_path',
          host: 'host',
          user: 'user',
          key: 'key'
        },
        sftp_endpoints: [
          {
            file_path: 'file_path',
            host: 'host',
            user: 'user',
            password: 'password'
          }
        ],
        instance_names: []
      }
    end
  end
end
