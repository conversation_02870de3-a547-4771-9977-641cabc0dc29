# frozen_string_literal: true

FactoryBot.define do
  factory :user do
    sequence(:canvas_id) { |n| n }

    contact_information { Faker::Internet.email }
    email { Faker::Internet.email }
    first_name { Faker::Name.first_name }
    grade_level { nil }
    last_name { Faker::Name.last_name }
    login_id { Faker::Internet.email }
    name { "#{first_name} #{last_name}" }
    primary_school_id { nil }
    sis_id { Faker::IdNumber.valid }
    sortable_name { "#{last_name}, #{first_name}" }
    workflow_state { 'active' }

    trait :with_pseudonym do
      after(:create) do |user|
        create(:pseudonym, user: user)
      end
    end
  end
end
