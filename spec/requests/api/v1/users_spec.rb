# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::UsersController, type: :request do
  let(:organization) { current_organization }
  let!(:account) { create(:account) }
  let!(:observer) { create(:user) }
  let!(:teacher) { create(:user) }
  let!(:student) { create(:user) }
  let!(:student2) { create(:user) }
  let!(:course) { create(:course, account:) }
  let(:date) { Date.current }
  let(:session) do
    create(:session, user: observer, organization:, panda_pal_organization_id: organization.id)
  end
  let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }

  before do
    # Student Enrollments
    create :enrollment, :student, course:, user: student
    create :enrollment, :student, course:, user: student2
    # Observer Enrollments
    create :enrollment, :observer, course:, user: observer
    create :enrollment, :observer, course:, user: observer, observed_user: student
    create :enrollment, :observer, course:, user: observer, observed_user: student2
    # Teacher Enrollments
    create :enrollment, :teacher, course:, user: teacher
  end

  describe 'GET #observed_users' do
    let!(:attendance) do
      create(:attendance, user: student, course:, attendance_date: date, time_in_minutes: nil)
    end

    it 'returns observed users with missing attendance data' do
      get observed_users_api_v1_users_path(organization_id: organization.id), params: default_params

      expect(response).to have_http_status(:ok)
      expect(json_response[:users]).to include(
        include({
                  canvas_id: student.canvas_id.to_s,
                  grade_level: student.grade_level,
                  sortable_name: student.sortable_name,
                  sis_id: student.sis_id,
                  missing_attendance_count: 1,
                  learning_coaches: [
                    {
                      email: observer.email,
                      sortable_name: observer.sortable_name,
                      contact_information: observer.contact_information,
                      contact_information2: observer.contact_information2
                    }
                  ]
                }),
        include({
                  canvas_id: student2.canvas_id.to_s,
                  grade_level: student2.grade_level,
                  sortable_name: student2.sortable_name,
                  sis_id: student2.sis_id,
                  learning_coaches: [
                    {
                      email: observer.email,
                      sortable_name: observer.sortable_name,
                      contact_information: observer.contact_information,
                      contact_information2: observer.contact_information2
                    }
                  ]
                })
      )
    end

    it 'orders users by sortable_name' do
      get observed_users_api_v1_users_path(organization_id: organization.id), params: default_params

      expect(response).to have_http_status(:ok)
      users_response = json_response[:users]
      sorted_names = users_response.pluck(:sortable_name).sort
      expect(users_response.pluck(:sortable_name)).to eq(sorted_names)
    end
  end
end
