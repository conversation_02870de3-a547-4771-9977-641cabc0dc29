# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Api::V1::AttendanceController, type: :request do
  let(:organization) { current_organization }
  let!(:account) { create(:account) }
  let!(:observer) { create(:user) }
  let!(:teacher) { create(:user) }
  let!(:student) { create(:user) }
  let!(:course) { create(:course, account:) }
  let(:date) { Date.current }
  let(:session) do
    create(:session, user: observer, organization:, panda_pal_organization_id: organization.id, launch_context: account)
  end
  let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }

  before do
    # Student Enrollments
    create :enrollment, :student, course:, user: student
    # Observer Enrollments
    create :enrollment, :observer, course:, user: observer
    create :enrollment, :observer, course:, user: observer, observed_user: student
    # Teacher Enrollments
    create :enrollment, :teacher, course:, user: teacher
  end

  describe 'POST #record' do
    let(:valid_params) do
      {
        date: date.to_s,
        courses: [
          { canvas_id: course.canvas_id, time_in_minutes: 120, organization_name: current_organization.name,
            enrollment_canvas_user_id: student.canvas_id }
        ]
      }
    end

    let(:invalid_params) do
      {
        date: date.to_s,
        courses: [
          { canvas_id: course.canvas_id, time_in_minutes: -5, organization_name: current_organization.name,
            enrollment_canvas_user_id: student.canvas_id }
        ]
      }
    end

    context 'when valid params are provided' do
      it 'records the attendance successfully' do
        expect do
          post api_v1_user_attendance_record_path(organization_id: organization.id, user_id: student.canvas_id),
               params: default_params.merge(valid_params)
        end.to change(Attendance, :count).by(1)

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['message']).to eq('Attendance recorded successfully')
      end
    end

    context 'when invalid params are provided' do
      it 'returns an error' do
        post api_v1_user_attendance_record_path(organization_id: organization.id, user_id: student.canvas_id),
             params: default_params.merge(invalid_params)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['error']).to eq('Failed to record attendance')
      end
    end
  end

  describe 'GET #missing' do
    before do
      create(:attendance, user: student, course:, attendance_date: date, time_in_minutes: nil)
    end

    context 'when only_count is true' do
      it 'returns the count of missing attendance records' do
        get api_v1_attendance_missing_path(organization_id: organization.id, user_id: student.canvas_id),
            params: default_params.merge(only_count: true)

        expect(response).to have_http_status(:ok)
        expect(json_response[:missing_attendance_count]).to eq(1)
      end
    end

    context 'when only_count is blank' do
      it 'returns the missing attendance records' do
        get api_v1_attendance_missing_path(organization_id: organization.id, user_id: student.canvas_id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:missing_attendance_count]).to eq(1)
        expect(json_response[:data]).to include({
                                                  canvas_id: student.canvas_id,
                                                  attendance_date: date.strftime('%d-%m-%Y'),
                                                  sortable_name: student.sortable_name
                                                })
      end
    end
  end

  describe 'POST #attendance' do
    let(:api_key) { create :api_key }
    let(:authorization) { "Bearer #{api_key.raw_token}" }
    let(:valid_params) do
      {
        course_sis_id: course.sis_id,
        date: date.to_s,
        minutes: 120
      }
    end
    let(:invalid_course_params) do
      {
        course_sis_id: 'INVALID_SIS',
        date: date.to_s,
        minutes: 120
      }
    end
    let(:invalid_minutes_params) do
      {
        course_sis_id: course.sis_id,
        date: date.to_s,
        minutes: -10
      }
    end

    context 'when valid params are provided' do
      it 'records attendance successfully' do
        expect do
          post api_v1_post_attendance_path(organization_id: organization.id, student_id: student.canvas_id),
               params: default_params.merge(valid_params), headers: { 'HTTP_AUTHORIZATION' => authorization }
        end.to change(Attendance, :count).by(1)

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['message']).to eq('Attendance recorded successfully')
      end
    end

    context 'when an invalid course SIS ID is provided' do
      it 'returns a course not found error' do
        post api_v1_post_attendance_path(organization_id: organization.id, student_id: student.canvas_id),
             params: default_params.merge(invalid_course_params), headers: { 'HTTP_AUTHORIZATION' => authorization }

        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['error']).to eq('Course Not Found')
      end
    end

    context 'when invalid minutes are provided' do
      it 'returns an invalid minutes error' do
        post api_v1_post_attendance_path(organization_id: organization.id, student_id: student.canvas_id),
             params: default_params.merge(invalid_minutes_params), headers: { 'HTTP_AUTHORIZATION' => authorization }

        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['error']).to eq('Invalid Minutes')
      end
    end

    context 'when the course is locked or the date is out of bounds' do
      let(:locked_date_params) do
        {
          student_id: student.canvas_id,
          course_sis_id: course.sis_id,
          date: (course.resolved_end_date + 1.day).to_s,
          minutes: 120
        }
      end

      it 'returns an invalid date error' do
        post api_v1_post_attendance_path(organization_id: organization.id, student_id: student.canvas_id),
             params: default_params.merge(locked_date_params), headers: { 'HTTP_AUTHORIZATION' => authorization }

        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['error']).to eq('Invalid Date')
      end
    end
  end
end
