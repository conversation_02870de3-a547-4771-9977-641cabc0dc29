# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Accounts', type: :request do
  let(:organization) { current_organization }
  let!(:account) { create(:account, lockout_date_setting: :days_after_course_end_date) }
  let!(:account_admin) { create(:user) }
  let(:session) do
    create(:session, user: account_admin, organization:, panda_pal_organization_id: organization.id,
                                                         launch_context: account, roles: 'Account Admin')
  end
  let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }

  let(:lockout_date) { '2024-12-31' }
  let(:valid_params) do
    { account: { enabled: true, lockout_date: lockout_date, lockout_date_setting: 'specific_date',
                 default_time_to_complete: 10 } }
  end
  let(:invalid_params) do
    { account: { enabled: nil, lockout_date: nil, lockout_date_setting: nil, lockout_days: nil } }
  end

  before do
    # Account Admin Role
    create(:role, :account_admin, canvas_account_id: account.canvas_id)
  end

  describe 'PUT /api/v1/accounts/:id' do
    context 'with valid params' do
      it 'updates the lockout_date_setting from old value to new value' do
        put api_v1_account_path(organization_id: organization.id, id: account.canvas_id),
                                  params: default_params.merge(valid_params)

        expect(response).to have_http_status(:ok)
        expect(account.reload.lockout_date_setting).to eq 'specific_date'
      end
    end

    context 'with invalid params' do
      it 'does not update the account and returns an error response' do
        put api_v1_account_path(organization_id: organization.id, id: account.canvas_id),
                                  params: default_params.merge(invalid_params)

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response[:error]).to include('Failed to update account')
        expect(json_response[:details]).to include("Lockout days can't be blank, Lockout days is not a number")
      end
    end

    context 'when account is not found' do
      it 'returns a 404 error' do
        put api_v1_account_path(organization_id: organization.id, id: 'invalid'),
                                  params: default_params.merge(valid_params)

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
