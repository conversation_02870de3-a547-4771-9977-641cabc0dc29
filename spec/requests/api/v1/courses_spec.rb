require 'rails_helper'

RSpec.describe 'Api::V1::Courses', type: :request do
  let(:organization) { current_organization }
  let!(:account) { create(:account) }
  let!(:account_admin) { create(:user) }
  let!(:observer) { create(:user) }
  let!(:teacher) { create(:user) }
  let!(:student) { create(:user) }
  let!(:term) { create :term }
  let!(:course) { create :course, term: }
  let!(:account_course) { create :course, term:, account: }
  let!(:account_blueprint_course) { create :course, term:, account:, is_blueprint: true }

  let(:session) do
    create(:session, user: account_admin, organization:, panda_pal_organization_id: organization.id,
                                                         launch_context: account, roles: 'Account Admin')
  end
  let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }

  before do
    # Account Admin Role
    create(:role, :account_admin, canvas_account_id: account.id)

    # Student Enrollments
    create :enrollment, :student, course:, user: student
    # Observer Enrollments
    create :enrollment, :observer, course:, user: observer
    create :enrollment, :observer, course:, user: observer, observed_user: student
    # Teacher Enrollments
    create :enrollment, :teacher, course:, user: teacher
  end

  # describe 'GET /api/v1/courses/blueprint' do
  #   context 'when logged in as School Administrator (Sub-Account Administrator)' do
  #     before do
  #       get api_v1_courses_blueprint_path(organization_id: current_organization.id), params: default_params
  #     end

  #     it 'returns blueprint courses for account' do
  #       expect(json_response[:courses].length).to be(1)
  #       expect(json_response[:courses]).to include(
  #         canvas_id: account_blueprint_course.canvas_id,
  #         name: account_blueprint_course.name,
  #         default_time_to_complete: account_blueprint_course.default_time_to_complete,
  #         resolved_start_date: account_blueprint_course.resolved_start_date.as_json,
  #         resolved_end_date: account_blueprint_course.resolved_end_date.as_json,
  #         grade_level: account_blueprint_course.grade_level,
  #         subject: account_blueprint_course.subject,
  #       )
  #     end
  #   end
  # end
end
