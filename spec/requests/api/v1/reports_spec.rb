# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Api::V1::Reports', type: :request do
  let(:organization) { current_organization }
  let!(:account) { create(:account) }
  let!(:account_admin) { create(:user) }
  let(:session) do
    create(:session, user: account_admin, organization:, panda_pal_organization_id: organization.id,
                                                         launch_context: account, roles: 'Account Admin')
  end
  let(:default_params) { { organization_id: organization.id, session_key: session.session_key } }

  let!(:report) { create :report, :complete, created_by_user_id: account_admin.canvas_id }

  before do
    # Account Admin Role
    create(:role, :account_admin, canvas_account_id: account.canvas_id)

    # get api_v1_courses_blueprint_path(organization_id: current_organization.id), params: default_params
  end

  describe 'POST /api/v1/reports' do
    context 'when the report is successfully created' do
      it 'creates a new report and processes it' do
        expect_any_instance_of(Report).to receive(:process_report)

        post api_v1_reports_path(organization_id: current_organization.id), params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:id]).not_to be_nil
        expect(json_response[:status]).to eq('queued')
      end
    end

    context 'when an error occurs' do
      before do
        allow_any_instance_of(Report).to receive(:save!).and_raise(StandardError, 'Something went wrong')
      end

      it 'logs the error and returns an unprocessable entity status' do
        post api_v1_reports_path(organization_id: current_organization.id), params: default_params

        expect(response).to have_http_status(:unprocessable_entity)
        expect(json_response[:error]).to eq('Something went wrong')
      end
    end
  end

  describe 'GET /api/v1/reports/:id' do
    context 'when the report exists' do
      it 'retrieves the specified report' do
        get api_v1_report_path(organization_id: current_organization.id, id: report.id), params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:id]).to eq(report.id)
        expect(json_response[:status]).to eq('complete')
      end
    end

    context 'when the report does not exist' do
      it 'returns a 404 error' do
        get api_v1_report_path(organization_id: current_organization.id, id: 'invalid-id'), params: default_params

        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'GET /api/v1/reports/latest_completed_report' do
    context 'when a completed report exists' do
      it 'retrieves the latest completed report for the current user' do
        get latest_completed_report_api_v1_reports_path(organization_id: current_organization.id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response[:id]).to eq(report.id)
        expect(json_response[:status]).to eq('complete')
        expect(json_response[:created_by_user_id]).to eq(account_admin.canvas_id)
      end
    end

    context 'when no completed reports exist' do
      it 'returns a 200 with no report' do
        Report.destroy_all

        get latest_completed_report_api_v1_reports_path(organization_id: current_organization.id),
            params: default_params

        expect(response).to have_http_status(:ok)
        expect(json_response).to be_empty
      end
    end
  end
end
