# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Attendance, type: :model do
  describe 'associations' do
    it { should belong_to(:user).with_foreign_key(:canvas_user_id).class_name('User').with_primary_key(:canvas_id) }
    it {
      should belong_to(:course).with_foreign_key(:canvas_course_id).class_name('Course').with_primary_key(:canvas_id)
    }
  end

  describe 'validations' do
    subject { create(:attendance) }

    it { should validate_presence_of(:attendance_date) }
    it { should validate_numericality_of(:time_in_minutes).only_integer.is_greater_than_or_equal_to(0).allow_nil }

    it 'validates uniqueness of attendance_date scoped to canvas_user_id and canvas_course_id' do
      user = create(:user)
      course = create(:course)
      create(:attendance, user: user, course: course, attendance_date: '2024-11-12')

      duplicate_attendance = build(:attendance, user: user, course: course, attendance_date: '2024-11-12')
      expect(duplicate_attendance).not_to be_valid
      expect(duplicate_attendance.errors[:attendance_date])
        .to include('attendance for this user and course on this date already exists')
    end
  end

  describe 'callbacks' do
    let!(:attendance) { create(:attendance, time_in_minutes: 10) }

    it 'increments audit log count by 1 when time_in_minutes is updated' do
      expect do
         attendance.update!(time_in_minutes: 20, actioning_user: create(:user))
      end.to change { AuditLog.count }.by(1)
    end
  end
end
