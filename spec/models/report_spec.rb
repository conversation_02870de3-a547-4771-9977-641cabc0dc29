require 'rails_helper'

RSpec.describe Report, type: :model do
  describe 'associations' do
    it { should have_one_attached(:file) }
  end

  describe 'enums' do
    it {
      should define_enum_for(:status).with_values(queued: 0, processing: 1, complete: 2,
                                                             complete_with_errors: 3, failed: 4)
    }
  end

  describe 'scopes' do
    describe '.by_user_id' do
      let!(:report1) { create(:report, created_by_user_id: 1) }
      let!(:report2) { create(:report, created_by_user_id: 2) }

      it 'returns reports for a specific user' do
        expect(Report.by_user_id(1)).to include(report1)
        expect(Report.by_user_id(1)).not_to include(report2)
      end
    end
  end

  describe 'serialize' do
    it 'serializes report_arguments as a Hash' do
      report = create(:report, report_arguments: { key: 'value' })
      expect(report.report_arguments).to be_a(Hash)
    end
  end

  describe 'instance methods' do
    describe '#process_report' do
      let(:report) { create(:report) }

      it 'enqueues the ExportReportJob with the report ID' do
        expect(Reports::ExportReportJob).to receive(:perform_later).with(report.id)
        report.process_report
      end
    end
  end
end
