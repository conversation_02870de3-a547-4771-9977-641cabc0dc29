# frozen_string_literal: true

require 'rails_helper'

RSpec.describe User, type: :model do
  describe '#observer?' do
    context 'across shards' do
      with_multiple_shards(
        shards: { alt: 'learning_coach_attendance_test_alternate' },
        tenant_names: %w[primary alt:alt],
      )

      before :all do
        create(:organization, name: 'primary', shard: 'default', canvas_shard_id: 1)
        create(:organization, name: 'alt', shard: 'alt', canvas_shard_id: 2)
      end

      let!(:org1) { PandaPal::Organization.find_by(name: 'primary') }
      let!(:org2) { PandaPal::Organization.find_by(name: 'alt') }

      before :each do
        org1.switch_tenant
        @observer = create(:user, canvas_id: 1)
        @student = create(:user, canvas_id: 2)
        UserShardAssociation.create(user: @observer, organization: org2)
        UserShardAssociation.create(user: @student, organization: org2)

        org2.switch_tenant
        @sharded_observer = create(:user, canvas_id: 1 + PandaPal::Organization::SHARD_OFFSET)
        @sharded_student = create(:user, canvas_id: 2 + PandaPal::Organization::SHARD_OFFSET)
      end

      it 'returns true if the user has observer enrollments on any shard' do
        org1.switch_tenant
        create(:enrollment, :observer, user: @observer, canvas_associated_user_id: @student.canvas_id)
        expect(@observer.observer?).to be true
      end

      it 'returns true if the user has observer enrollments on another shard' do
        org2.switch_tenant
        create(:enrollment, :observer, user: @sharded_observer, canvas_associated_user_id: @sharded_student.canvas_id)

        org1.switch_tenant
        expect(@observer.observer?).to be true
      end

      it 'returns false if the user has no observer enrollments on any shard' do
        org1.switch_tenant
        create(:enrollment, :student, user: @observer)

        org2.switch_tenant
        create(:enrollment, :teacher, user: @sharded_observer)

        org1.switch_tenant
        expect(@observer.observer?).to be false
      end
    end
  end
end
