require 'rails_helper'

RSpec.describe A<PERSON><PERSON><PERSON>, type: :model do
  let(:user) { create(:user) }
  let(:api_key) { create(:api_key, user: user) }

  describe 'api_token associations' do
    it { should belong_to(:user) }
  end

  describe 'api_token callbacks' do
    it 'generates raw_token before create' do
      api_key1 = build(:api_key, user: user)
      expect { api_key1.save }.to change { api_key1.raw_token }.from(nil).to(be_a(String))
    end

    it 'generates token_digest before create' do
      api_key1 = build(:api_key, user: user)
      expect { api_key1.save }.to change { api_key1.token_digest }.from(nil).to(be_a(String))
    end

    it 'generates token before create' do
      api_key1 = build(:api_key, user: user)
      expect { api_key1.save }.to change { api_key1.token }.from(nil).to(be_a(String))
    end
  end

  describe 'find_by_token!' do
    it 'finds the ApiKey by its token' do
      api_key.save
      found_api_key = ApiKey.find_by!(token: api_key.token)
      expect(found_api_key).to eq(api_key)
    end

    it 'raises an error when the token is invalid' do
      expect { ApiKey.find_by_token!('invalid_token') }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end

  describe 'find_by_token' do
    it 'finds the ApiKey by its token' do
      api_key.save
      found_api_key = ApiKey.find_by(token: api_key.token)
      expect(found_api_key).to eq(api_key)
    end

    it 'returns nil when the token is invalid' do
      expect(ApiKey.find_by_token('invalid_token')).to be_nil
    end
  end

  describe 'generate_digest' do
    it 'generates a token digest based on the token' do
      token = '77972N2NFkQYoYrpvhXZ9rKYKwhRhB'
      token_digest = ApiKey.generate_digest(token)
      expected_digest = OpenSSL::HMAC.hexdigest('SHA256', ApiKey::HMAC_SECRET_KEY, token)
      expect(token_digest).to eq(expected_digest)
    end
  end

  describe 'token generation' do
    it 'generates a token that is a base58 string' do
      api_key.save
      expect(api_key.token.length).to eq(30)
    end

    it 'generates a raw_token that is a base58 string' do
      api_key.save
      expect(api_key.raw_token.length).to eq(30)
    end
  end
end
