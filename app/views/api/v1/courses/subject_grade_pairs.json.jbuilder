# frozen_string_literal: true

json.courses @paginated_data.result do |course|
  json.default_time_to_complete           course.default_daily_time_to_complete
  json.grade_level                        course.grade_level
  json.subject                            course.subject
  json.label                              course.label
end

json.pagination do
  json.page @paginated_data.page
  json.per_page @paginated_data.per_page
  json.total_pages @paginated_data.total_pages
  json.total_items @paginated_data.count
end
