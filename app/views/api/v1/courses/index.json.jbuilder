# frozen_string_literal: true

json.courses @courses do |course|
  switch_tenant(course.organization_name) do
    json.partial! 'api/v1/courses/course', course: course

    json.lock_date                        course.lock_date&.strftime('%d-%m-%Y')
    json.locked                           course.locked?
  end

  json.time_in_minutes         @attendance_for_date_data[course.organization_name][course.canvas_id]
  json.total_time_in_minutes   @attendance_to_date_data[course.organization_name][course.canvas_id]

  json.start_date              course.resolved_start_date&.strftime('%d-%m-%Y')
  json.end_date                course.resolved_end_date&.strftime('%d-%m-%Y')

  json.enrollment_canvas_user_id        course.enrollment_canvas_user_id.to_s
  json.organization_name                course.organization_name
  json.organization_id                  course.organization_id

  json.disable_attendance_recording(
    if @is_stride_admin
      false
    else
      begin
        date = params[:date].to_date
        !(date.between?(course.resolved_start_date, course.resolved_end_date))
      rescue
        true
      end
    end
  )
end
