# frozen_string_literal: true

if @import
  json.id                   @import.id
  json.status               @import.status&.titleize
  json.file_name            @import.file.filename
  json.records_updated      @import.records_updated
  json.error_records        @import.error_records
  json.error_message        @import.error_message
  json.completed_at         @import.completed_at&.utc&.strftime('%m-%d-%Y at %H:%M:%S %p UTC')
  json.initiated_at         @import.created_at&.utc&.strftime('%m-%d-%Y at %H:%M:%S %p UTC')
end
