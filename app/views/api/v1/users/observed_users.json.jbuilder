# frozen_string_literal: true

json.users @users do |user|
  json.canvas_id                      user.canvas_id.to_s
  json.sortable_name                  user.sortable_name
  json.sis_id                         user.sis_id
  json.grade_level                    user.grade_level
  json.organization_id                user.organization_id
  json.organization_name              user.organization_name
  json.missing_attendance_count       @missing_attendance_data[user.canvas_id]

  json.learning_coaches do
    user.all_active_learning_coaches.each do |observer|
      json.child! do
        json.email                    observer.email
        json.sortable_name            observer.sortable_name
        json.contact_information      observer.contact_information
        json.contact_information2     observer.contact_information2
      end
    end
  end
end
