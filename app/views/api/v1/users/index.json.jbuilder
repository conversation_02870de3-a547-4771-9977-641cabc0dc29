# frozen_string_literal: true

json.users @paginated_data.result do |user|
  json.partial! 'api/v1/users/user', user: user

  json.primary_school user.primary_school_name

  json.learning_coaches do
    user.active_learning_coaches.each do |observer|
      json.child! do
        json.email                    observer.email
        json.sortable_name            observer.sortable_name
        json.contact_information      observer.contact_information
        json.contact_information2     observer.contact_information2
      end
    end
  end

  json.missing_attendance_count @missing_attendance_data[user.canvas_id]
end

json.pagination do
  json.page @paginated_data.page
  json.per_page @paginated_data.per_page
  json.total_pages @paginated_data.total_pages
  json.total_items @paginated_data.count
end
