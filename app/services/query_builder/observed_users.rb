# frozen_string_literal: true

class QueryBuilder::ObservedUsers < QueryBuilder::BaseQueryBuilder
  attr_accessor :organization_id, :organization_name

  def records
    users = []

    observer.against_shards do |obs|
      @organization_id = current_organization.id
      @organization_name = current_organization.name

      obs = User.find_by(canvas_id: obs.canvas_id)
      users += fetch_users(obs)
    end

    # Show uniq records in case if same user is present in Original & Sharded Account
    users.uniq { |u| u.canvas_id % PandaPal::Organization::SHARD_OFFSET }
         .sort_by(&:sortable_name)
  end

  private

  def fetch_users(observer)
    @users = observer.active_enrollment_observed_users
                          .joins(enrollments: { course: :account })
                          .where(enrollments: { courses: { accounts: { enabled: true } } })
                          .includes(:active_learning_coaches)
                          .order(:sortable_name)
                          .distinct
                          .select(sql_selects)
  end

  def observer
    @observer ||= options[:observer]
  end

  def sql_selects
    <<~SQL.squish
      #{organization_id} AS organization_id,
      '#{organization_name}' AS organization_name,
      users.canvas_id,
      users.sortable_name,
      users.sis_id,
      users.grade_level
    SQL
  end
end
