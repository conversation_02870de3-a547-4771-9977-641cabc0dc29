class QueryBuilder::ObserverStudentCourses < QueryBuilder::BaseQueryBuilder
  attr_reader :organization_id, :organization_name, :attendance_to_date_data, :attendance_for_date_data

  def records
    @attendance_to_date_data = {}
    @attendance_for_date_data = {}
    all_courses = []

    student.against_shards do |shard_student|
      @organization_id = current_organization.id
      @organization_name = current_organization.name

      shard_student = User.find_by(canvas_id: shard_student.canvas_id)
      courses = fetch_courses(shard_student)

      canvas_course_ids = courses.map(&:canvas_id)
      merge_attendance_data(shard_student, canvas_course_ids)

      all_courses += courses
    end

    all_courses
  end

  private

  def fetch_courses(shard_student)
    shard_student.courses
              .active_student_enrollments
              .includes(:blueprint_course, :term, :account)
              .order(:name)
              .enabled_accounts
              .distinct
              .select(sql_selects)
  end

  def merge_attendance_data(shard_student, canvas_course_ids)
    @attendance_to_date_data[organization_name] ||= {}
    @attendance_for_date_data[organization_name] ||= {}

    @attendance_to_date_data[organization_name].merge!(
      Attendance.where(user: shard_student, canvas_course_id: canvas_course_ids)
                .group(:canvas_course_id)
                .sum(:time_in_minutes)
    )

    @attendance_for_date_data[organization_name].merge!(
      Attendance.where(attendance_date: date, user: shard_student, canvas_course_id: canvas_course_ids)
                .group(:canvas_course_id)
                .pluck(:canvas_course_id, 'SUM(time_in_minutes) AS total_time')
                .to_h
                .transform_values(&:presence)
    )
  end

  def sql_selects
    <<~SQL.squish
      #{organization_id} AS organization_id,
      '#{organization_name}' AS organization_name,
      courses.*,
      enrollments.canvas_user_id AS enrollment_canvas_user_id
    SQL
  end

  def student
    @student ||= options[:student]
  end

  def date
    @date ||= options[:date]
  end
end
