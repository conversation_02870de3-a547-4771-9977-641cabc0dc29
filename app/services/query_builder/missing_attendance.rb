# frozen_string_literal: true

class QueryBuilder::MissingAttendance < QueryBuilder::BaseQueryBuilder
  attr_accessor :organization_id, :organization_name

  def observed_user_records
    return {} if observer.blank?

    records = []

    observer.against_shards do |obs|
      local_observer = User.find_by(canvas_id: obs.canvas_id)
      next unless local_observer
      student_ids = observed_student_ids(local_observer)
      student_ids = student_ids & [ options[:user_id].to_i ] if options[:user_id]

      next if student_ids.blank?

      active_course_ids = students_course_ids(student_ids)
      attendance_records = Attendance
        .joins(:user)
        .where(canvas_user_id: student_ids, time_in_minutes: nil, canvas_course_id: active_course_ids)
        .order('attendance_date ASC, users.sortable_name ASC')
        .select(
          'DISTINCT attendance_date, canvas_user_id AS canvas_id, users.sortable_name'
        )

      records += attendance_records
    end

    records.flatten
  end

  def normalized_sorted_records(records)
    shard_offset = PandaPal::Organization::SHARD_OFFSET

    non_sharded_ids = records.map(&:canvas_id).select { |id| id < shard_offset }.to_set

    # Group by normalized id + date
    grouped = records.group_by do |r|
      [ r.canvas_id % shard_offset, r.attendance_date ]
    end

    uniq_records = grouped.map do |(normalized_id, date), recs|
      # Prefer record whose canvas_user_id is non-sharded if exists
      preferred = recs.find { |r| non_sharded_ids.include?(r.canvas_id % shard_offset) } || recs.first

      # Always normalize canvas_user_id if non-sharded id exists
      preferred_id = non_sharded_ids.include?(normalized_id) ? normalized_id : preferred.canvas_id

      {
        canvas_id: preferred_id,
        attendance_date: preferred.attendance_date.strftime('%d-%m-%Y'),
        sortable_name: preferred.sortable_name
      }
    end

    uniq_records.sort_by { |r| [ r[:attendance_date], r[:sortable_name] ] }
  end

  private

  def observer
    @observer ||= options[:observer]
  end

  def observed_student_ids(user)
    user.active_enrollment_observed_users
                .joins(enrollments: { course: :account })
                .where(enrollments: { courses: { accounts: { enabled: true } } })
                .pluck(:canvas_id)
  end

  def students_course_ids(student_ids)
    Enrollment.where(canvas_user_id: student_ids).active.student.pluck(:canvas_course_id).uniq
  end
end
