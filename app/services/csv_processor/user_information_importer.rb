# frozen_string_literal: true

class CsvProcessor::UserInformationImporter < CsvProcessor::Base
  def process_row(row, idx)
    sis_id = sid_id_from_row(row)
    return if sis_id.blank?

    user = User.joins(:pseudonyms).where(pseudonyms: { sis_id: }).limit(1).take
    return unless user

    attributes = parse_row(row)
    user.update(attributes)
  rescue StandardError => e
    rows_with_errors << idx
    Rails.logger.error "Error processing row: #{row.inspect}. Error: #{e.message}. Backtrace: #{e.backtrace}"
  end

  private

  def parse_row(row)
    case key
    when :learning_coach_contact_information
      attrs = {
        contact_information: row['Email Address'],
        contact_information2: row['Phone Number']
      }
      attrs.transform_values { |value| parse_attribute(attrs.key(value), value) }
    when :users
      attrs = {
        primary_school_id: fetch_primary_school_id(row),
        grade_level: row['grades']
      }
      attrs.transform_values { |value| parse_attribute(attrs.key(value), value) }
    else
      raise ArgumentError, "Unknown key: #{key}"
    end
  end

  def fetch_primary_school_id(row)
    Rails.cache.fetch("canvas_id_for_account_by_sis_id_#{row['orgSourcedIds']}") do
      Account.find_by(sis_id: row['orgSourcedIds'])&.canvas_id
    end
  end

  def sid_id_from_row(row)
    case key
    when :learning_coach_contact_information
      row['User SIS ID']
    when :users
      row['sourcedId']
    else
      raise ArgumentError, "Unknown key: #{key}"
    end
  end
end
