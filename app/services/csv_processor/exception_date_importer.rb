# frozen_string_literal: true

class CsvProcessor::ExceptionDateImporter < CsvProcessor::Base
  def process_row(row, idx)
    start_date = Date.parse(row['Start Date'])
    end_date = Date.parse(row['End Date'])
    return if start_date.blank? || end_date.blank?

    if row['Status'] == 'deleted'
      ExceptionDate.where(date: start_date..end_date).destroy_all
    else
      (start_date..end_date).each do |date|
        ExceptionDate.find_or_create_by!(date:, exception_type: :blackout, name: 'Blackout')
      end
    end
  rescue StandardError => e
    rows_with_errors << idx
    Rails.logger.error "Error processing row: #{row.inspect}. Error: #{e.message}. Backtrace: #{e.backtrace}"
  end
end
