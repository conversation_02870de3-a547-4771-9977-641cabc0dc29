# frozen_string_literal: true

class CsvProcessor::Base
  DATE_FORMATS = [ '%m/%d/%Y', '%Y-%m-%d' ].freeze
  BOOLEAN_VALUES = %w[y yes 1 t true].freeze
  BATCH_SIZE = 1000

  attr_reader :file_path, :key, :model
  attr_accessor :rows_with_errors

  def self.call(file_path, key, model)
    new(file_path, key, model).call
  end

  def initialize(file_path, key, model)
    @file_path = file_path
    @key = key
    @model = model
    @rows_with_errors = []
  end

  def call
    CSV.foreach(file_path, headers: true).with_index(2) do |row, idx|
      process_row(row, idx)
    end
    self
  rescue StandardError => e
    Rails.logger.error "Error importing file: #{e.message}"
  end

  def success?
    rows_with_errors.empty?
  end

  private

  def process_row(row, idx)
    raise NotImplementedError, 'Subclasses must implement this method'
  end

  def parse_attribute(model_attr, value)
    return value if value.blank?

    column_type = model.columns_hash[model_attr.to_s].type

    case column_type
    when :string, :text
      value.strip
    when :boolean
      to_boolean(value)
    when :float
      value.to_f
    when :integer
      value.to_i
    when :date
      parse_date(value)
    else
      raise StandardError, "Unknown column type: #{column_type}"
    end
  end

  def parse_date(value)
    DATE_FORMATS.each do |format|
      return Date.strptime(value, format)
    rescue ArgumentError
      next
    end
    raise Date::Error, "Invalid date format: #{value}"
  end

  def to_boolean(value)
    BOOLEAN_VALUES.include?(value.to_s.downcase)
  end
end
