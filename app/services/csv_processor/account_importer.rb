  # frozen_string_literal: true

  class CsvProcessor::AccountImporter < CsvProcessor::Base
    def process_row(row, idx)
      sis_id = sid_id_from_row(row)
      return if sis_id.blank?

      account = Account.find_by(sis_id:)
      return unless account

      attributes = parse_row(row)
      account.update(attributes)
    rescue StandardError => e
      rows_with_errors << idx
      Rails.logger.error "Error processing row: #{row.inspect}. Error: #{e.message}. Backtrace: #{e.backtrace}"
    end
  end

  private

  def sid_id_from_row(row)
    case key
    when :terms
      row['orgSourcedId']
    else
      raise ArgumentError, "Unknown key: #{key}"
    end
  end

  def parse_row(row)
    case key
    when :terms
      attrs = {
        school_year: row['schoolYear']
      }
      attrs.transform_values { |value| parse_attribute(attrs.key(value), value) }
    else
      raise ArgumentError, "Unknown key: #{key}"
    end
  end
