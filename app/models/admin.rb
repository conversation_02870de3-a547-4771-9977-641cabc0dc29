# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#


class Admin < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable

  canvas_sync_features :defaults

  validates :canvas_id, uniqueness: true, presence: true
  belongs_to :account, primary_key: :canvas_id, foreign_key: :canvas_account_id, optional: true
  belongs_to :user, primary_key: :canvas_id, foreign_key: :canvas_user_id, optional: true
  belongs_to :role, primary_key: :canvas_id, foreign_key: :canvas_role_id, optional: true

  api_syncable({
    canvas_id: :id,
    role_name: :role,
    canvas_role_id: :role_id,
    canvas_user_id: ->(r) { r['user']['id'] },
    # NOTICE: The :account_id field is added by CanvasSync - it is not included in the Canvas API response
    canvas_account_id: :account_id,
    workflow_state: :workflow_state
  }, ->(api) {
    admins = api.account_admins(canvas_account_id).all_pages!
    admin_data = admins.find { |admin| admin['id'] == canvas_id }
    raise Footrest::HttpError::NotFound unless admin_data.present?
    admin_data
  })
end
