# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#


class Role < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable

  canvas_sync_features :defaults

  validates :canvas_id, uniqueness: true, presence: true
  has_many :admins, foreign_key: :canvas_role_id, primary_key: :canvas_id

  api_syncable({
    canvas_id: :id,
    label: :label,
    base_role_type: :base_role_type,
    canvas_account_id: ->(r) { r.dig('account', 'id') },
    permissions: :permissions,
    workflow_state: :workflow_state
  }, ->(api) { api.get("/api/v1/accounts/#{canvas_account_id}/roles/#{canvas_id}") })

  def self.for_labels(labels, account)
    built_ins = []
    labels = labels.split(',') if labels.is_a?(String)
    custom_labels = Array(labels).reject do |l|
      if DEFAULT_ROLE_LABELS.include?(l)
        built_ins << l
      elsif l == 'Account Admin' || l == 'AccountAdmin'
        built_ins << 'AccountMembership'
      else
        next
      end
      true
    end

    off_shard_role_account_ids = Rails.cache.fetch('off_shard_account_ids', expires_in: 6.hours) do
      # Find all the roles where the account is not in the accounts table.
      # Add the uniq accounts from that query to the off_shard_role_accounts
      Role.where.not(canvas_account_id: Account.pluck(:canvas_id)).pluck(:canvas_account_id).uniq
    end

    account_ids = Rails.cache.fetch([ self.class.name, 'AccountAncestry', account.canvas_id ], expires_in: 6.hours) do
      if account.respond_to?(:path_ids)
        account.path.pluck(:canvas_id)
      else
        [].tap do |pids|
          acc = account
          loop do
            break unless acc
            pids.unshift(acc.canvas_id)
            acc = acc.canvas_parent
          end
        end
      end
    end

    if off_shard_role_account_ids.present?
      account_ids = account_ids + off_shard_role_account_ids
    end

    where(workflow_state: 'built_in', base_role_type: built_ins)
      .or(where.not(workflow_state: 'built_in').where(label: custom_labels, canvas_account_id: account_ids))
  end
end
