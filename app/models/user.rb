# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class User < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable

  include CrossShardUser

  canvas_sync_features :defaults

  # include CanvasSync::Concerns::LiveEventSync
  # around_process_live_event do |user, blk|
  #   blk.call
  # rescue Footrest::HttpError::Unauthorized => e
  #   # This can happen when a new user is created, but hasn't setup a login on Canvas yet.
  #   Rails.logger.info("Failed to fetch user #{canvas_user_id}: #{e.backtrace}")
  # end

  validates :canvas_id, uniqueness: true, presence: true
  has_many :pseudonyms, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :enrollments, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :admins, primary_key: :canvas_id, foreign_key: :canvas_user_id
  has_many :admin_roles, through: :admins, source: :role
  has_many :courses, through: :enrollments
  has_many :api_keys, primary_key: :canvas_id, foreign_key: :canvas_user_id, dependent: :nullify

  belongs_to :primary_school, class_name: 'Account', optional: true, primary_key: :canvas_id

  # Observer-Observee relationship through user_ovservers
  # HACK: Our LTI is limited
  # has_many :observer_links, class_name: 'UserObserver', foreign_key: :observing_user_id, primary_key: :canvas_id
  # has_many :observed_users, through: :observer_links, source: :observed_user

  # has_many :observing_links, class_name: 'UserObserver', foreign_key: :observed_user_id, primary_key: :canvas_id
  # has_many :observing_users, through: :observing_links, source: :observing_user

  # has_many :active_observer_links, -> { where(workflow_state: 'active') }, class_name: 'UserObserver',
  #          foreign_key: :observing_user_id, primary_key: :canvas_id
  # has_many :active_observed_users, through: :active_observer_links, source: :observed_user

  # has_many :active_observing_links, -> { where(workflow_state: 'active') }, class_name: 'UserObserver',
  #          foreign_key: :observed_user_id, primary_key: :canvas_id
  # has_many :active_observing_users, through: :active_observing_links, source: :observing_user

  # Observer-Observee relationship through enrollments
  has_many :observer_enrollments, -> { where.not(canvas_associated_user_id: nil) },
           class_name: 'Enrollment', foreign_key: :canvas_user_id, primary_key: :canvas_id
  has_many :enrollment_observed_users, -> { distinct }, through: :observer_enrollments, source: :observed_user

  has_many :observed_enrollments, -> { where.not(canvas_associated_user_id: nil) },
           class_name: 'Enrollment', foreign_key: :canvas_associated_user_id, primary_key: :canvas_id
  has_many :enrollment_observed_users, through: :observer_enrollments, source: :observed_user
  has_many :learning_coaches, -> { distinct }, through: :observed_enrollments, source: :observing_user

  has_many :active_observer_enrollments,
           -> { where(workflow_state: [ 'active', 'concluded' ]).where.not(canvas_associated_user_id: nil) },
           class_name: 'Enrollment', foreign_key: :canvas_user_id, primary_key: :canvas_id
  has_many :active_enrollment_observed_users, lambda {
    distinct
  }, through: :active_observer_enrollments, source: :observed_user

  has_many :active_observed_enrollments,
           -> { where(workflow_state: [ 'active', 'concluded' ]).where.not(canvas_associated_user_id: nil) },
           class_name: 'Enrollment', foreign_key: :canvas_associated_user_id, primary_key: :canvas_id
  has_many :active_learning_coaches, lambda {
    distinct
  }, through: :active_observed_enrollments, source: :observing_user

  has_many :attendances, foreign_key: :canvas_user_id, primary_key: :canvas_id

  has_many :active_teacher_enrollments, lambda {
    where(workflow_state: [ 'active', 'concluded' ], base_role_type: 'TeacherEnrollment')
  }, primary_key: :canvas_id, foreign_key: :canvas_user_id, class_name: 'Enrollment'

  has_many :active_teaching_courses, lambda {
    where(workflow_state: [ 'active', 'concluded' ])
  }, through: :active_teacher_enrollments, source: :course

  scope :active, -> { where(workflow_state: [ 'active', 'concluded' ]) }

  scope :active_students, lambda {
    active
      .joins(enrollments: :course)
      .where(enrollments: {
               workflow_state: [ 'active', 'concluded' ],
               base_role_type: 'StudentEnrollment', courses: { workflow_state: [ 'active', 'concluded' ] }
             })
      .distinct
  }

  scope :active_observers, lambda {
    active
      .joins(enrollments: :course)
      .where(enrollments: {
               workflow_state: [ 'active', 'concluded' ],
               base_role_type: 'ObserverEnrollment', courses: { workflow_state: [ 'active', 'concluded' ] }
             })
      .distinct
  }

  scope :active_students_in_course_ids, lambda { |canvas_course_ids|
    active
      .joins(enrollments: :course)
      .where(enrollments: {
               workflow_state: [ 'active', 'concluded' ], base_role_type: 'StudentEnrollment',
               courses: { workflow_state: [ 'active', 'concluded' ], canvas_id: canvas_course_ids }
             })
      .distinct
  }

  scope :active_students_for_account, lambda { |canvas_account_id|
    active
      .joins(enrollments: :course)
      .where(enrollments: {
              workflow_state: [ 'active', 'concluded' ], base_role_type: 'StudentEnrollment',
              courses: { workflow_state: [ 'active', 'concluded' ], canvas_account_id: }
            })
      .distinct
  }

  api_syncable({
    sis_id: :sis_user_id,
    email: :email,
    login_id: :login_id,
    name: :name,
    sortable_name: :sortable_name,
    first_name: :short_name
  }, ->(api) { api.user_detail(canvas_id) })

  # def all_active_observed_users
  #   # TODO Support cross-shard
  #   User.includes(:pseudonyms).where(id: active_observed_users.ids + active_enrollment_observed_users.ids)
  # end

  # def all_active_observing_users
  #   # TODO Support cross-shard
  #   User.includes(:pseudonyms).where(id: active_observing_users.ids + active_learning_coaches.ids)
  # end

  delegate :name, :sis_id, to: :primary_school, prefix: true, allow_nil: true

  def observer?
    against_shards do |shard_user|
      return true if shard_user.observer_enrollments.exists?
    end
    false
  end

  def teacher?(course_id)
    enrollments.teacher.where(canvas_course_id: course_id).exists?
  end

  def primary_pseudonym
    pseudonyms.where(workflow_state: 'active').order(:canvas_id).first
  end

  # Returns all Cross Shard Learning Coaches
  def all_active_learning_coaches
    primary_record.against_shards do |shard_user|
      shard_user = User.find_by(canvas_id: shard_user.canvas_id)
      shard_user.active_learning_coaches.select(
        <<~SQL.squish
          users.email,
          users.sortable_name,
          users.contact_information,
          users.contact_information2
        SQL
      ).to_a
    end.flatten
    .uniq { |u| [ u.email, u.sortable_name ] }
    .sort_by(&:sortable_name)
  end
end
