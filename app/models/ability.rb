# frozen_string_literal: true

class Ability
  include CanCan::Ability
  include PandaPal::Concerns::AbilityHelper
  include CanvasSync::Concerns::AbilityHelper

  def initialize(user, rails_session:, panda_session:)
    @user = user
    @panda_pal_session = panda_session
    @rails_session = rails_session

    # Root Account Administrator / Stride Administrator
    # Can launch from both account and course levels
    if user_is_stride_admin?
      can :launch_from, [ :account, :course ]
      can :manage, :reports
    end

    # Sub-Account Administrator / School Administrator
    # Can launch from both account and course levels
    if user_is_school_admin?
      can :launch_from, [ :account, :course ]
      can :access, :blueprint_courses
      can :access, :subject_grade_pairs
      can :update, :account
      can :manage, :reports
    end

    # Account-Linked Observer
    # Can launch from both course and user levels
    if user_is_observer?
      can :launch_from, [ :course, :user ]
    end

    # Teacher role
    # Can only launch from the course level
    if user_is_teacher?
      can :launch_from, :course
    end
  end

  def canvas_roles
    @canvas_roles ||= Role.for_labels(panda_pal_session.canvas_role_labels, launch_account)
  end

  def user_is_school_admin?
    @user_is_school_admin ||= canvas_account_roles.pluck(:base_role_type).any?('AccountMembership')
  end

  def user_is_stride_admin?
    @user_is_stride_admin ||= Account.includes(:admins).root_account.admins.exists?(canvas_user_id: @user.canvas_id)
  end

  def user_is_teacher?
    @user_is_teacher ||= @user.teacher?(panda_pal_session.custom_lti_params[:course_id])
  end

  def user_is_observer?
    @user_is_observer ||= @user.observer?
  end

  def record_attendance?
    user_is_stride_admin? || user_is_observer? || user_is_teacher? || user_is_school_admin?
  end

  # There could be more roles
  def view_attendance?
    user_is_stride_admin? || user_is_observer? || user_is_teacher? || user_is_school_admin?
  end
end
