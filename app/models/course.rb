# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class Course < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable

  canvas_sync_features :defaults

  # include CanvasSync::Concerns::LiveEventSync
  # after_process_live_event do
  #   if account.nil?
  #     acc = Account.new(canvas_id: canvas_account_id)
  #     acc.sync_from_api
  #   end
  # end

  validates :canvas_id, uniqueness: true, presence: true
  belongs_to :term, foreign_key: :canvas_term_id, primary_key: :canvas_id, optional: true
  belongs_to :account, primary_key: :canvas_id, foreign_key: :canvas_account_id
  belongs_to :blueprint_course, class_name: 'Course', primary_key: :canvas_id, foreign_key: :canvas_blueprint_course_id,
                                optional: true

  has_many :enrollments, primary_key: :canvas_id, foreign_key: :canvas_course_id

  has_many :users, through: :enrollments

  has_many :students, lambda {
    where(enrollments: { workflow_state: [ 'active', 'concluded' ], base_role_type: 'StudentEnrollment' }).distinct
  }, through: :enrollments, source: :user

  scope :blueprint, -> { where(is_blueprint: true) }

  scope :by_account_id, ->(canvas_account_id) { where(canvas_account_id:) }

  scope :active, -> { where(workflow_state: [ 'active', 'concluded' ]) }
  scope :active_student_enrollments, lambda {
    joins(:enrollments)
      .where(enrollments: { workflow_state: [ 'active', 'concluded' ], base_role_type: 'StudentEnrollment' },
                                            workflow_state: [ 'active', 'concluded' ])
      .distinct
  }

  scope :enabled_accounts, -> { joins(:account).where(account: { enabled: true }) }

  scope :for_account, lambda { |account|
    # Return an empty ActiveRecord relation if account doesn't have a start_date
    return none unless account&.start_date.present?

    where('resolved_start_date <= ? AND resolved_end_date >= ?', account.end_date, account.start_date)
  }

  scope :for_date, ->(date) { where('resolved_start_date <= :date AND resolved_end_date >= :date', date: date) }

  sync_mapping(reset: false) do
    link_column :canvas_blueprint_course_id, type: :integer
    link_column :is_blueprint, type: :boolean
  end

  api_syncable({
                 sis_id: :sis_course_id,
                 course_code: :course_code,
                 name: :name,
                 workflow_state: :workflow_state,
                 canvas_term_id: :enrollment_term_id,
                 canvas_account_id: :account_id,
                 start_at: :start_at,
                 end_at: :end_at
               }, ->(api) { api.course(canvas_id) })

  def lock_date
    return Date.current unless resolved_end_date

    if account.lockout_date_setting_days_after_course_end_date?
      resolved_end_date + account.lockout_days
    else
      account.lockout_date
    end
  end

  def default_daily_time_to_complete
    default_time_to_complete

    # TODO: Deprecate Account level default time
    # Order of priority: Account/School > Course
    # account.default_time_to_complete || default_time_to_complete
  end

  def locked?(date = Date.current)
    date > lock_date
  end
end
