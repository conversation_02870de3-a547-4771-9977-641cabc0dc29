# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#


class Account < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable
  # include CanvasSync::Concerns::Account::Ancestry # Add support for the ancestry Gem
  # include CanvasSync::Concerns::LiveEventSync

  enum lockout_date_setting: { days_after_course_end_date: 0, specific_date: 1 }, _prefix: true

  canvas_sync_features :defaults

  validates :canvas_id, uniqueness: true, presence: true
  validates :lockout_days, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  validates :lockout_date, presence: true, if: -> { lockout_date_setting == 'specific_date' }

  validates :school_year, allow_nil: true, format: {
    with: /\A\d{4}-\d{4}\z/,
    message: 'must be in the format YYYY-YYYY'
  }
  validate :validate_school_year_values, if: -> { school_year.present? }

  belongs_to :canvas_parent, class_name: 'Account', optional: true,
                             primary_key: :canvas_id, foreign_key: :canvas_parent_account_id
  has_many :admins, primary_key: :canvas_id, foreign_key: :canvas_account_id
  has_many :sub_accounts, class_name: 'Account',
                          primary_key: :canvas_id, foreign_key: :canvas_parent_account_id

  has_many :courses, primary_key: :canvas_id, foreign_key: :canvas_account_id

  scope :active, -> { where.not(workflow_state: 'deleted') }
  # scope :should_canvas_sync, -> { active } # Optional - uses .active if not given

  api_syncable({
    name: :name,
    workflow_state: :workflow_state,
    canvas_parent_account_id: :parent_account_id
  }, ->(api) { api.account(canvas_id) })

  def self.root_account
    find_by(canvas_parent_account_id: nil)
  end

  def start_date
    @start_date ||= Date.new(start_year) if start_year
  end

  def end_date
    @end_date ||= Date.new(end_year, 12, 31) if end_year
  end

  def is_root_account?
    canvas_parent_account_id.nil?
  end

  private

  def validate_school_year_values
    begin
      start_year, end_year = school_year.split('-').map(&:to_i)
      raise ArgumentError if start_year > end_year

      raise ArgumentError unless (1900..3000).cover?(start_year) && (1900..3000).cover?(end_year)
    rescue ArgumentError
      errors.add(:school_year, 'must contain valid years where the start year is less than or equal to the end year')
    end
  end

  # Helper method to split and parse years
  def extract_years
    return [ nil, nil ] unless school_year.present? && school_year.match(/\A\d{4}-\d{4}\z/)

    school_year.split('-').map(&:to_i)
  end

  def start_year
    @start_year ||= extract_years.first
  end

  def end_year
    @end_year ||= extract_years.last
  end
end
