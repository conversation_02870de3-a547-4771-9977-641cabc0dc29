class ApiKey < ApplicationRecord
  HMAC_SECRET_KEY = ENV.fetch('HMAC_SECRET_KEY').freeze

  validates :name, presence: true

  belongs_to :user, primary_key: :canvas_id, foreign_key: :canvas_user_id

  before_create :generate_raw_token
  before_create :generate_token_digest

  before_create :generate_token

  attr_accessor :raw_token

  def self.find_by_token!(token)
    find_by!(token_digest: generate_digest(token))
  end

  def self.find_by_token(token)
    find_by(token_digest: generate_digest(token))
  end

  def self.generate_digest(token)
    OpenSSL::HMAC.hexdigest('SHA256', HMAC_SECRET_KEY, token)
  end

  private

  def generate_raw_token
    self.raw_token = SecureRandom.base58(30)
  end

  def generate_token_digest
    self.token_digest = self.class.generate_digest(raw_token)
  end

  def generate_token
    self.token = SecureRandom.base58(30)
  end
end
