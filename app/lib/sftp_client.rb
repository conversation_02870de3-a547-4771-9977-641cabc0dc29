# frozen_string_literal: true

class SftpClient
  DOWNLOAD_DIR = '/tmp'.freeze

  def download_latest_file(dir)
    return unless latest_file_name(dir).present?

    client.download!("#{sftp_file_path}/#{latest_file_name(dir)}")
  end

  def latest_file_name(sub_dir)
    @_latest_file_name ||= client.dir.glob("#{sftp_file_path}/#{sub_dir}", '*.csv').max_by do |file|
      file.attributes&.mtime
    end&.name
  rescue Errno::ENOENT
    ''
  end

  def csv_file_names
    client.dir.glob(sftp_file_path, '*.csv').map(&:name)
  end

  def download_file(file_name)
    client.download!("#{sftp_file_path}/#{file_name}", "#{DOWNLOAD_DIR}/#{file_name}")
  end

  private

  def client
    @client ||= Net::SFTP.start(sftp_host, sftp_user, key_data: sftp_key)
  end

  def settings
    @settings ||= current_organization.settings
  end

  def sftp_file_path
    settings[:sftp][:file_path]
  end

  def sftp_host
    settings[:sftp][:host]
  end

  def sftp_user
    settings[:sftp][:user]
  end

  def sftp_key
    settings[:sftp][:key]
  end
end
