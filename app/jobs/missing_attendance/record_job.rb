class MissingAttendance::RecordJob < MissingAttendance::BaseJob
  queue_as :default

  def perform(*args)
    return if skip_attendance_recording?

    active_enabled_accounts.each do |account|
      record_attendance_for_account(account)
    end
  end

  private

  def record_attendance_for_account(account)
    account.courses
           .active_student_enrollments
           .for_account(account)
           .for_date(date)
           .includes(:enrollments)
           .find_each do |course|
      record_attendance_for_course(course)
    end
  end

  def record_attendance_for_course(course)
    course.enrollments.active.student.find_each do |enrollment|
      Attendance.find_or_create_by!(canvas_user_id: enrollment.canvas_user_id, attendance_date: date, course: course)
    end
  end
end
