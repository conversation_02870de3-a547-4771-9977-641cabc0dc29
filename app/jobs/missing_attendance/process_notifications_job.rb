class MissingAttendance::ProcessNotificationsJob < MissingAttendance::BaseJob
  queue_as :default

  def perform(recipient_id, student_id, date)
    return unless valid_inputs?(recipient_id, student_id, date)

    student = User.find_by!(canvas_id: student_id)
    body_params = build_notification_params(recipient_id, student, date)

    canvas_sync_client.post('/api/v1/conversations', body_params)
  end

  private

  def valid_inputs?(recipient_id, student_id, date)
    recipient_id.present? && student_id.present? && date.present?
  end

  def build_notification_params(recipient_id, student, date)
    {
      subject: 'Attendance Not Recorded',
      body: <<~BODY.strip,
        This is an automated message to notify you that attendance for #{student.name}
        is not recorded on #{date.strftime('%A, %B %d, %Y')}.
      BODY
      recipients: [ recipient_id ],
      force_new: true
    }
  end
end
