class MissingAttendance::QueueNotificationsJob < MissingAttendance::BaseJob
  queue_as :default

  def perform(*_args)
    return if skip_attendance_recording?

    active_enabled_accounts.find_each do |account|
      notify_missing_attendance_for_account(account)
    end
  end

  private

  def notify_missing_attendance_for_account(account)
    notify_roles(account, 'TeacherEnrollment') do |user, course_ids|
      student_ids = students_with_missing_attendance(course_ids)
      queue_notifications(user.canvas_id, student_ids)
    end

    notify_roles(account, 'ObserverEnrollment') do |observer, _course_ids|
      student_ids = associated_students_with_missing_attendance(observer, account)
      queue_notifications(observer.canvas_id, student_ids)
    end
  end

  def notify_roles(account, role_type)
    active_users(account, role_type).find_each do |user|
      course_ids = courses_for_user(user, account).pluck(:canvas_id)
      yield(user, course_ids) if course_ids.present?
    end
  end

  def active_users(account, role_type)
    User.joins(enrollments: :course).where(
      enrollments: {
        base_role_type: role_type,
        workflow_state: 'active',
        courses: { workflow_state: 'active', canvas_account_id: account.canvas_id }
      }
    ).distinct
  end

  def courses_for_user(user, account)
    user.courses.for_account(account).active
  end

  def students_with_missing_attendance(course_ids)
    Attendance.where(
      time_in_minutes: nil,
      attendance_date: date,
      canvas_course_id: course_ids
    ).pluck(:canvas_user_id).uniq
  end

  def associated_students_with_missing_attendance(observer, account)
    student_ids = observer.enrollments.joins(:course).where(
      workflow_state: 'active',
      base_role_type: 'ObserverEnrollment',
      courses: { workflow_state: 'active', canvas_account_id: account.canvas_id }
    ).pluck(:canvas_associated_user_id).uniq

    Attendance.where(
      time_in_minutes: nil,
      attendance_date: date,
      canvas_user_id: student_ids
    ).pluck(:canvas_user_id).uniq
  end

  def queue_notifications(recipient_id, student_ids)
    student_ids.each do |student_id|
      MissingAttendance::ProcessNotificationsJob.perform_later(recipient_id, student_id, date)
    end
  end
end
