class MissingAttendance::BaseJob < ApplicationJob
  queue_as :default

  private

  def date
    @date ||= Date.yesterday
  end

  # Skip attendance recording if the date is a weekend, holiday or blackout
  def skip_attendance_recording?
    weekend? || holiday_or_blackout?
  end

  def weekend?
    date.saturday? || date.sunday?
  end

  def holiday_or_blackout?
    ExceptionDate.where(date: date).exists?
  end

  def active_enabled_accounts
    @active_enabled_accounts ||= Account.active.where(enabled: true)
  end
end
