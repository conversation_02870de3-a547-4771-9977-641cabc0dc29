# frozen_string_literal: true

class Imports::DefaultAttendanceValueJob < Imports::BaseImportJob
  DATA_MAP = Import::COURSE_DATA_HEADERS

  def handle_import
    records_updated_count = 0
    processed_rows = []

    parse_file do |row, _ln|
      validate_required_fields(row)

      records_updated = false
      skip_if_duplicate(processed_rows, row)

      courses = Course.where(subject: trimmed_value(row[DATA_MAP[:subject]]),
                              grade_level: trimmed_value(row[DATA_MAP[:grade]])
                            )
      courses.update_all(default_time_to_complete: trimmed_value(row[DATA_MAP[:minutes]]))

      records_updated = true if courses.any?
      records_updated_count += 1 if records_updated
      processed_rows << row
    end
    update_records_counts(records_updated_count)
  end

  private

  def skip_if_duplicate(processed_rows, row)
    return if processed_rows.exclude?(row)

    raise Imports::FailedImportRowError, 'Duplicate record. The row could not be processed.'
  end

  def validate_required_fields(row)
    raise Imports::FailedImportRowError, 'Attribute Subject not found' if trimmed_value(row[DATA_MAP[:subject]]).blank?
    raise Imports::FailedImportRowError, 'Attribute Grade not found' if trimmed_value(row[DATA_MAP[:grade]]).blank?
    raise Imports::FailedImportRowError, 'Attribute Minutes not found' if default_time_to_complete(row).blank?
    unless default_time_to_complete(row).to_i.in?(0..1440)
      raise Imports::FailedImportRowError, 'Minutes must be between 0 and 1,440'
    end
  end

  def default_time_to_complete(row)
    trimmed_value(row[DATA_MAP[:minutes]])
  end
end
