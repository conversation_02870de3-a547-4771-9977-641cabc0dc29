# frozen_string_literal: true

module Imports
  class FailedJobError < StandardError; end
  class FailedImportRowError < StandardError; end

  class BaseImportJob < ApplicationJob
    queue_as :default

    attr_accessor :import, :failed_rows, :local_file_path

    def perform(import_id)
      @failed_rows = {}
      @import = Import.find(import_id)
      stop_retry if @import.status == 'failed'
      start_import
      @local_file_path = import_file
      validate_blank_csv!
      validate_file(import.file.filename)
      handle_import
      complete_import
      # send_dashboard_notificaiton
    rescue StandardError => e
      fail_import(e.message)
      # send_dashboard_notificaiton
      Rails.logger.error "Import with ID: #{import_id} failed to complete."
      Rails.logger.error "Error: #{e.message}"
      Rails.logger.error e.backtrace
      raise e
    end

    def handle_import
      raise 'Must implement in subclass'
    end

    private

    def import_file
      file_path = "#{Dir.tmpdir}/#{import.file.filename}"
      File.open(file_path, 'wb') do |file|
        file.write(import.file.download)
        yield file if block_given?
      end
      file_path
    end

    def parse_file
      file = File.open(local_file_path)
      f = file.read
      encoding_name = f.encoding.name

      CSV.foreach(local_file_path, headers: true, encoding: "bom|#{encoding_name}").with_index(0) do |row, ln|
        yield row, ln
      rescue FailedImportRowError => e
        # add 2 to the ln due to zero index and header row
        error = { "Line #{ln + 2}" => e.message }
        @failed_rows.merge! error
      end
    end

    def update_records_counts(count)
      import.update(records_updated: count)
    end

    def complete_import
      status = @failed_rows.empty? ? :complete : :complete_with_errors
      import.update(status:, error_records: failed_rows, completed_at: Time.now)
    end

    def start_import
      import.update(status: :processing)
    end

    def fail_import(message)
      import.update(status: :failed, error_records: failed_rows, error_message: message)
    end

    def stop_retry
      @failed_rows = @import.error_records
      raise FailedJobError, @import.error_message
    end

    def validate_file(file_name)
      raise "#{file_name} File not found on device" unless File.exist?(local_file_path)
      raise "#{file_name} File is empty" if File.empty?(local_file_path)
    end

    def validate_blank_csv!
      return unless CSV.read(local_file_path, headers: true, encoding: 'iso-8859-1:utf-8').count.zero?

      error = { 'Line 1' => 'CSV is Blank' }
      @failed_rows.merge! error
    end

    def trimmed_value(str)
      str.to_s.strip
    end
  end
end
