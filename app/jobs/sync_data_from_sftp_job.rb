# frozen_string_literal: true

class SyncDataFromSftpJob < ApplicationJob
  FILES = {
    accounts: 'academicSessions.csv',
    users: 'users.csv',
    courses: 'classes.csv'
  }.freeze

  LOCAL_DIR = Rails.root.join('tmp/sftp_downloads')
  BATCH_SIZE = 1000

  def perform
    return Rails.logger.error("SFTP settings not configured for #{current_organization.name}") if sftp_endpoints.blank?

    sftp_endpoints.each do |endpoint|
      process_sftp_endpoint(endpoint)
    end
  end

  private

  def sftp_endpoints
    current_organization.settings[:sftp_endpoints]
  end

  def instance_names
    current_organization.settings[:instance_names] || []
  end

  def process_sftp_endpoint(endpoint)
    Rails.logger.info("Connecting to SFTP: #{endpoint[:host]}")

    SftpClientV2.new(endpoint, instance_names).fetch_files.each do |zip_file|
      process_zip(zip_file, endpoint[:host], endpoint[:user])
    rescue StandardError => e
      Rails.logger.error "Error processing SFTP Files #{endpoint[:host]}: #{e.inspect}"
      raise e
    end
  end

  def process_zip(zip_file, server_name, user_name)
    destination_dir = LOCAL_DIR.join(current_organization.name, server_name, user_name)

    Rails.logger.info("Processing ZIP from #{server_name}: #{zip_file}")

    ZipExtractor.extract(zip_file, destination_dir).each do |csv_file|
      process_csv_file(csv_file) if FILES.value?(File.basename(csv_file))
    ensure
      delete_file(csv_file)
    end

    delete_file(zip_file)
  end

  def delete_file(path)
    File.delete(path) if File.exist?(path)
  end

  def process_csv_file(csv_file)
    key = FILES.key(File.basename(csv_file))
    Rails.logger.info("Processing CSV: #{csv_file}")

    send("process_#{key}_csv", csv_file) if key
  end

  def process_users_csv(csv_file)
    process_csv_batch(csv_file) do |rows|
      sis_data_map = rows.each_with_object({}) do |row, hash|
        hash[row['identifier']] = {
          grade_level: if row['grades'].present?
                         begin
                           JSON.parse(row['grades']).first
                         rescue StandardError
                           nil
                         end
                       end,
          primary_school_sis_id: row['orgSourcedIds']&.split(',')&.first
        }
      end

      next if sis_data_map.blank?

      users_to_update = []
      User.joins(:pseudonyms)
          .where(pseudonyms: { sis_id: sis_data_map.keys })
          .find_each(batch_size: 500) do |user|
        user.pseudonyms.pluck(:sis_id).reject(&:blank?).each do |sis_id|
          data = sis_data_map[sis_id]
          next if data.blank?

          updated_attrs = {}
          grade_level = data[:grade_level]
          grade_level = grade_level.to_i.to_s if grade_level&.match?(/^\d+$/)
          updated_attrs[:grade_level] = grade_level
          if data[:primary_school_sis_id].present?
            updated_attrs[:primary_school_id] =
              fetch_primary_school_id(data[:primary_school_sis_id])
          end
          next unless updated_attrs.any?

          user.assign_attributes(updated_attrs)
          users_to_update << user
          break
        end
      end

      if users_to_update.any?
        User.import(users_to_update,
                    on_duplicate_key_update: %i[grade_level primary_school_id])
      end
    end
  end

  def process_csv_batch(csv_file, &)
    CSV.foreach(csv_file, headers: true).each_slice(BATCH_SIZE, &)
  end

  def fetch_primary_school_id(primary_school_sis_id)
    Rails.cache.fetch("canvas_id_for_account_by_sis_id_#{primary_school_sis_id}", skip_nil: true) do
      Account.find_by(sis_id: primary_school_sis_id)&.canvas_id
    end
  end

  def process_accounts_csv(csv_file)
    process_csv_batch(csv_file) do |rows|
      sis_school_map = rows.each_with_object({}) do |row, hash|
        next if row['sourcedId'].blank?

        # Remove last part form the sourcedId
        sourced_id = row['sourcedId'].rpartition('-').first
        hash[sourced_id] = row['schoolYear'] if row['sourcedId'].present? && row['schoolYear'].present?
      end

      next if sis_school_map.blank?

      accounts_to_update = []
      Account.where(sis_id: sis_school_map.keys).find_each(batch_size: 500) do |account|
        next unless sis_school_map[account.sis_id].present?

        # School Year in Format: 2023-2024 (YYYY-YYYY)
        new_school_year = "#{sis_school_map[account.sis_id]}-#{sis_school_map[account.sis_id]}"
        account.assign_attributes(school_year: new_school_year)
        accounts_to_update << account
      end

      Account.import(accounts_to_update, on_duplicate_key_update: [ :school_year ]) if accounts_to_update.any?
    end
  end

  def process_courses_csv(csv_file)
    process_csv_batch(csv_file) do |rows|
      sis_data_map = rows.index_by { |row| [ row['sourcedId'], row['courseSourcedId'] ] }.transform_values do |row|
        grade_level = parse_grades(row['grades'])
        next if grade_level.blank? || row['subjects'].blank?

        { grade_level: grade_level, subject: row['subjects'] }
      end

      update_courses(sis_data_map) if sis_data_map.present?
    end
  end

  def parse_grades(grades)
    return nil unless grades.present?

    begin
      JSON.parse(grades).first
    rescue StandardError
      nil
    end
  end

  def update_courses(sis_data_map)
    sis_ids = sis_data_map.keys.flatten.uniq
    courses_to_update = Course.where(sis_id: sis_ids).find_each(batch_size: 500)
                              .map { |course| prepare_course_update(course, sis_data_map) }
                              .compact

    Course.import(courses_to_update, on_duplicate_key_update: %i[grade_level subject]) if courses_to_update.any?
  end

  def prepare_course_update(course, sis_data_map)
    sis_id = sis_data_map.keys.find { |ids| ids.include?(course.sis_id) }
    data = sis_data_map[sis_id]
    return nil unless data

    update_attrs = {}
    update_attrs[:grade_level] = data[:grade_level] if data.dig(:grade_level).present?
    update_attrs[:subject] = data[:subject] if data.dig(:subject).present?

    return nil if update_attrs.empty?

    course.assign_attributes(update_attrs)
    course
  end
end
