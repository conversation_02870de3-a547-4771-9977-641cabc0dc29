# frozen_string_literal: true

class UpdateCourseStartEndDatesJob < ApplicationJob
  queue_as :default

  def perform(options = { force_update: false })
    courses_to_update = if options[:force_update]
                          Course.all
    else
                          Course.where(resolved_start_date: nil)
                                .or(Course.where(resolved_end_date: nil))
                                .or(Course.where.not('DATE(resolved_start_date) = DATE(start_at)'))
                                .or(Course.where.not('DATE(resolved_end_date) = DATE(end_at)'))
    end

    courses_to_update.find_each do |course|
      course.update(
        resolved_start_date: course.start_at || course.term.start_at,
        resolved_end_date: course.end_at || course.term.end_at)
    end
  end
end
