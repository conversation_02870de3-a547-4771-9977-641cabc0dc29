# frozen_string_literal: true

module Reports
  class FailedJobError < StandardError; end

  class CsvBaseReportJob < ApplicationJob
    attr_accessor :report, :csv_file_path

    around_perform :cleanup_working_dir

    def perform(report_id)
      @report = Report.find(report_id)
      stop_retry if @report.status == 'failed'
      start_report
      generate_report
      attach_csv
      complete_report
    rescue StandardError => e
      fail_report(e.message)
      Rails.logger.error "Report with ID: #{report_id} failed to complete."
      Rails.logger.error "Error: #{e.message}"
      Rails.logger.error e.backtrace
      raise e
    end

    def generate_report
      raise 'Must be implemented in subclass.'
    end

    def write_to_csv(report_file_name, headers, rows)
      @csv_file_path = csv_file_name(report_file_name)
      CSV.open(csv_file_path, 'w', write_headers: !rows.empty?, headers: headers) do |csv|
        if rows.empty?
          csv << [ 'No records found' ]
        else
          rows.each { |row| csv << row.values }
        end
      end
      csv_file_path
    end

    def csv_file_name(report_name)
      @csv_file_name ||= begin
        date = Time.now.strftime('%Y%m%d%H%M%S')
        File.join(working_dir, "#{report_name}_#{date}.csv")
      end
    end

    private

    def working_dir
      @working_dir ||= Dir.mktmpdir
    end

    private

    def cleanup_working_dir
      yield if block_given?
    ensure
      FileUtils.remove_entry @working_dir if @working_dir
    end

    def attach_csv
      raise 'CSV File not generated' unless File.exist?(csv_file_path)
      raise 'CSV File is empty' if File.empty?(csv_file_path)

      report.file.attach(
        io: File.open(csv_file_path),
        filename: csv_file_path.split('/').last,
        content_type: 'text/csv'
      )

      report.save!
    end

    def complete_report
      report.update(status: :complete, completed_at: Time.current)
    end

    def start_report
      report.update(status: :processing)
    end

    def stop_retry
      raise FailedJobError, report.error_message
    end

    def fail_report(message)
      report.update(status: :failed, error_message: message)
    end
  end
end
