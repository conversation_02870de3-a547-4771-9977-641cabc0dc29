# frozen_string_literal: true

class Reports::ExportReportJob < Reports::CsvBaseReportJob
  HEADERS = [
    'Student Name',
    'Student SIS ID',
    'Student\'s Grade Level',
    'Student\'s Learning Coach',
    'Missing Date'
  ].freeze

  def generate_report
    canvas_account_id = report.report_arguments[:account_id]

    missing_attendances = Attendance.missing

    if canvas_account_id
      missing_attendances = missing_attendances.joins(:course).where(course: { canvas_account_id: })
    end
    missing_attendances = missing_attendances.includes(user: :active_learning_coaches)

    rows = missing_attendances.select('DISTINCT canvas_user_id, attendance_date, canvas_course_id')
                              .map do |attendance|
                                {
                                'User Name' => attendance.user.sortable_name,
                                'User SIS ID' => attendance.user.sis_id,
                                'User\'s Grade Level' => attendance.user.grade_level,
                                'User Learning Coach' => attendance.user
                                                                   .active_learning_coaches
                                                                   .pluck(:sortable_name).join('; '),
                                'Attendance Date' => attendance.attendance_date
                                }
                                end


    write_to_csv('Missing_Attendance_Data_Report', HEADERS, rows)
  end
end
