class CanvasSyncStarterJob < ApplicationJob
  queue_as :default

  MODELS_TO_SYNC = %w[
    users
    pseudonyms
    courses
    accounts
    terms
    enrollments
    roles
    admins
    user_observers
  ]

  def perform(_opts = {})
    api_acc = canvas_sync_client.account('self', includes: [ 'global_id' ])
    current_organization.tap do |org|
      org.canvas_shard_id = (api_acc[:global_id] / PandaPal::Organization::SHARD_OFFSET).floor
      org.settings[:canvas][:default_time_zone] = api_acc[:default_time_zone]
      org.save!
    end

    job_chain = CanvasSync.default_provisioning_report_chain(
      MODELS_TO_SYNC,
      full_sync_every: 'sunday/1',
      updated_after: true,
      options: { roles: { show_inherited: true } }
    )
    job_chain.insert({ job: UpdateUserSisIdJob, options: {} })
    job_chain.insert({ job: RefreshCrossShardUsersJob })
    job_chain.insert({ job: UpdateCourseStartEndDatesJob, options: {} })
    job_chain.process!
  end
end
