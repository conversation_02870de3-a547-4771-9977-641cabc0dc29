# frozen_string_literal: true

class HostedDataPushJob < ApplicationJob
  queue_as :default

  SCHEMA = InstDataShipper::SchemaBuilder.build do
    table('attendance_data', model: Attendance) do
      source :local_table

      column :id
      column :canvas_user_id
      column :canvas_course_id
      column :attendance_date
      column :time_in_minutes
    end

    table('attendance_audit_log_data', model: AuditLog) do
      source :local_table

      column :id
      column :student_canvas_id
      column :student_sis_id, :"varchar(128)"
      column :student_name, :"varchar(128)"
      column :course_sis_id, :"varchar(128)"
      column :course_canvas_id
      column :course_name, :"varchar(128)"
      column :attendance_date
      column :previous_value
      column :updated_value
      column :actioning_user_sis_id, :"varchar(128)", from: ->(obj) { obj.actioning_user_sis_id.to_s }
      column :actioning_user_canvas_id
      column :actioning_user_name, :"varchar(128)", from: ->(obj) { obj.actioning_user_name.to_s }
      column :created_at, :TIMESTAMP_NTZ
    end

    table('attendance_school_data', model: Account) do
      source :local_table

      column :id
      column :canvas_id
      column :sis_id, :"varchar(128)", from: ->(obj) { obj.sis_id.to_s }
      column :name, :"varchar(128)"
      column :school_year, :"varchar(128)", from: ->(obj) { obj.school_year.to_s }
    end

    table('attendance_school_blackout_dates', model: ExceptionDate) do
      source :local_table

      column :id
      column :date
      column :exception_type, :"varchar(128)"
    end

    table('attendance_student_users_data', model: User, query: :active_students) do
      source :local_table

      column :id
      column :canvas_id
      column :sis_id, :"varchar(128)", from: ->(obj) { obj.sis_id.to_s }
      column :primary_school_sis_id, :"varchar(128)", from: ->(obj) { obj.primary_school_sis_id.to_s }
      column :grade_level, :"varchar(128)", from: ->(obj) { obj.grade_level.to_s }
    end

    table('attendance_learning_coach_users_data', model: User, query: :active_observers) do
      source :local_table

      column :id
      column :canvas_id
      column :sis_id, :"varchar(128)", from: ->(obj) { obj.sis_id.to_s }
      column :email_address, :"varchar(128)", from: ->(obj) { obj.contact_information.to_s } # Email
      column :phone_number, :"varchar(128)", from: ->(obj) { obj.contact_information2.to_s } # Phone Number
    end
  end

  Dumper = InstDataShipper::Dumper.define(schema: SCHEMA, include: [
                                            InstDataShipper::DataSources::LocalTables
                                          ])

  def perform
    destinations = current_organization.settings[:data_shipper_destinations]
    return if destinations.blank?

    Dumper.perform_dump(destinations)
  end
end
