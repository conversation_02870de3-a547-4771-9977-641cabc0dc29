import React from 'react'
import { Modal } from '@instructure/ui-modal'
import { Text } from '@instructure/ui-text'
import { Table } from '@instructure/ui'
import { Flex } from '@instructure/ui-flex'
import { CloseButton } from '@instructure/ui-buttons'

const ContactInformation = ({ isOpen, onClose, learningCoaches }) => {
  const renderCoachInfo = (coach, isFirst) => (
    <Flex direction="column" padding={isFirst ? 'none' : 'medium none none none'} key={coach.email}>
      <Flex.Item>
        <Text weight="bold" size="small">
          Learning Coach
        </Text>
      </Flex.Item>
      <Flex.Item padding="x-small none none none">
        <Text size="small">{coach.sortable_name}</Text>
      </Flex.Item>
      <Flex.Item padding="x-small none none none">
        <Table caption={false}>
          <Table.Head>
            <Table.Row>
              <Table.ColHeader id="email">
                <Text size="small">Email</Text>
              </Table.ColHeader>
              <Table.ColHeader id="phone">
                <Text size="small">Phone</Text>
              </Table.ColHeader>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            <Table.Row>
              <Table.Cell>{coach.contact_information}</Table.Cell>
              <Table.Cell>{coach.contact_information2}</Table.Cell>
            </Table.Row>
          </Table.Body>
        </Table>
      </Flex.Item>
    </Flex>
  )

  const renderCloseButton = () => (
    <CloseButton placement="end" offset="small" onClick={onClose} screenReaderLabel="Close" />
  )

  return (
    <Modal
      open={isOpen}
      onDismiss={onClose}
      label="Contact Information"
      shouldCloseOnDocumentClick
      size="small"
      closeButtonLabel="Close"
    >
      <Modal.Header>
        <Flex justifyItems="space-between" alignItems="center">
          <Flex.Item>
            <Text size="x-large">Contact Information</Text>
          </Flex.Item>
          <Flex.Item>{renderCloseButton()}</Flex.Item>
        </Flex>
      </Modal.Header>
      <Modal.Body>
        {learningCoaches.map((coach, index) => renderCoachInfo(coach, index === 0))}
      </Modal.Body>
    </Modal>
  )
}

export default ContactInformation
