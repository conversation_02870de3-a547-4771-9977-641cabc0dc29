import React, { useState, useEffect } from 'react'
import { Table, Text, Flex, Link, Spinner } from '@instructure/ui'
import {
  IconUserLine,
  IconAddressBookLine,
  IconMiniArrowUpLine,
  IconMiniArrowDownLine,
} from '@instructure/ui-icons'
import { Pagination } from '@instructure/ui-pagination'
import ContactInformation from '../shared/ContactInformation'
import MissingAttendanceTray from '../attendance/MissingAttendanceTray'

const StudentsTable = ({
  onStudentClick,
  students,
  loading,
  currentPage,
  setCurrentPage,
  totalPages,
  sortBy,
  ascending,
  handleSort,
}) => {
  const [currentStudent, setCurrentStudent] = useState(null)
  const [isContactInformationModalOpen, setIsContactInformationModalOpen] = useState(false)

  const openContactInformationModal = () => setIsContactInformationModalOpen(true)
  const closeContactInformationModal = () => setIsContactInformationModalOpen(false)

  const renderSortIcon = (column) => {
    if (sortBy === column) {
      return ascending ? <IconMiniArrowUpLine /> : <IconMiniArrowDownLine />
    }
    return null
  }

  const [isMissingAttendanceTrayOpen, setIsMissingAttendanceTrayOpen] = useState(false)

  const handleShowMissingAttendanceAlerts = async (student) => {
    setCurrentStudent(student)
    setIsMissingAttendanceTrayOpen(true)
  }

  const handleStudentTrayClick = (date, id) => {
    onStudentClick(id, date)
  }

  const renderTableHeaders = () => (
    <Table.Row>
      <Table.ColHeader
        id="student-name"
        onClick={() => handleSort('users.sortable_name')}
        as="button"
        textAlign="start"
      >
        Student {renderSortIcon('users.sortable_name')}
      </Table.ColHeader>
      <Table.ColHeader
        id="grade-level"
        onClick={() => handleSort('users.grade_level')}
        as="button"
        textAlign="center"
      >
        Grade Level {renderSortIcon('users.grade_level')}
      </Table.ColHeader>
      <Table.ColHeader id="school" textAlign="center">
        School
      </Table.ColHeader>
      <Table.ColHeader id="missing-attendance" textAlign="center">
        Missing Attendance
      </Table.ColHeader>
      <Table.ColHeader id="learning-coach" as="button" textAlign="center">
        Learning Coach
      </Table.ColHeader>
      <Table.ColHeader id="contact-info" textAlign="center">
        Contact Info
      </Table.ColHeader>
    </Table.Row>
  )

  const renderTableBody = () =>
    students.map((student, index) => (
      <Table.Row key={student.canvas_id}>
        <Table.Cell>
          <Flex direction="row">
            <Flex.Item margin="0 xx-small medium xx-small">
              <IconUserLine color="alert" />
            </Flex.Item>
            <Flex.Item>
              <Flex direction="column">
                <Flex.Item>
                  <Text weight="bold">
                    <Link onClick={() => onStudentClick(student.canvas_id)}>
                      {student.sortable_name}
                    </Link>
                  </Text>
                </Flex.Item>
                <Flex.Item>
                  <Text size="x-small">ID: {student.sis_id}</Text>
                </Flex.Item>
              </Flex>
            </Flex.Item>
          </Flex>
        </Table.Cell>
        <Table.Cell textAlign="center">{student.grade_level}</Table.Cell>
        <Table.Cell textAlign="center">{student.primary_school}</Table.Cell>
        <Table.Cell textAlign="center">
          <Text>
            <Link
              href="#"
              themeOverride={{
                focusOutlineWidth: 0,
              }}
              onClick={() => handleShowMissingAttendanceAlerts(student)}
            >
              {student.missing_attendance_count || 0} Alerts
            </Link>
          </Text>
        </Table.Cell>
        <Table.Cell textAlign="center">
          {student.learning_coaches?.map((coach) => coach.sortable_name)?.join('; ')}{' '}
        </Table.Cell>
        <Table.Cell textAlign="center">
          <Link
            onClick={() => {
              setCurrentStudent(student)
              openContactInformationModal()
            }}
          >
            <IconAddressBookLine />
          </Link>
        </Table.Cell>
      </Table.Row>
    ))

  if (loading) {
    return (
      <Flex alignItems="center" justifyItems="center" padding="xx-large">
        <Spinner renderTitle="Loading students..." />
      </Flex>
    )
  }

  return (
    <>
      <Table caption={false} layout="auto" hover>
        <Table.Head>{renderTableHeaders()}</Table.Head>
        <Table.Body>{renderTableBody()}</Table.Body>
      </Table>

      {totalPages > 1 && (
        <Pagination
          as="nav"
          margin="large small small"
          variant="compact"
          labelNext="Next Page"
          labelPrev="Previous Page"
          labelFirst="First Page"
          labelLast="Last Page"
          withFirstAndLastButton
          currentPage={currentPage}
          onPageChange={(nextPage) => setCurrentPage(nextPage)}
          totalPageNumber={totalPages}
          siblingCount={3}
          boundaryCount={2}
        />
      )}

      <ContactInformation
        isOpen={isContactInformationModalOpen}
        onClose={closeContactInformationModal}
        learningCoaches={currentStudent?.learning_coaches || []}
      />

      {currentStudent && (
        <MissingAttendanceTray
          setOpen={setIsMissingAttendanceTrayOpen}
          open={isMissingAttendanceTrayOpen}
          student={currentStudent}
          handleClick={handleStudentTrayClick}
        />
      )}
    </>
  )
}

export default StudentsTable
