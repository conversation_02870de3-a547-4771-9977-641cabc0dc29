import React from 'react'
import { Mo<PERSON>, Heading, Text, Button, CloseButton } from '@instructure/ui'

const AccountDisableModal = ({ open, onSubmit, onCancel }) => {
  const renderCloseButton = () => {
    return (
      <CloseButton placement="end" offset="small" onClick={onCancel} screenReaderLabel="Close" />
    )
  }

  return (
    <Modal
      open={open}
      onDismiss={onCancel}
      size="small"
      label="Account Disable Confirmation Modal"
      shouldCloseOnDocumentClick
    >
      <Modal.Header>
        {renderCloseButton()}
        <Heading>Turn Application Off</Heading>
      </Modal.Header>
      <Modal.Body>
        <Text weight="bold">User Access Effects</Text>

        <p>
          Selecting 'OFF' for this setting will prevent access to the school administrator
          application account settings and will prevent all sub-account users from accessing or
          viewing the Learning Coach Attendance Application. Please be aware of this and confirm
          this selection.
        </p>
      </Modal.Body>
      <Modal.Footer>
        <Button color="primary-inverse" onClick={onCancel}>
          Cancel
        </Button>
        <Button color="danger" margin="0 0 0 xx-small" onClick={onSubmit}>
          Yes, Turn Application Off
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default AccountDisableModal
