import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Text,
  But<PERSON>,
  CloseButton,
  Flex,
  TextInput,
  DateInput2,
  Alert,
} from '@instructure/ui'
import { RadioInput, RadioInputGroup } from '@instructure/ui-radio-input'
import { IconSaveLine } from '@instructure/ui-icons'
import moment from 'moment'

import DefaultDailyAttendanceTable from '../attendance/DefaultDailyAttendanceTable'
import DefaultDailyAttendanceTableV2 from '../attendance/DefaultDailyAttendanceTableV2'

import * as API from '../../utils/api'

const AccountSettingsModal = ({ open, onCancel, account, setAccount, updateAccountSettings }) => {
  const [initialAccountState, setInitialAccountState] = useState(account)
  const [submitEnabled, setSubmitEnabled] = useState(false)

  const [courses, setCourses] = useState([])
  const [initialCourses, setInitialCourses] = useState([])

  const [pairs, setPairs] = useState([])
  const [initialPairs, setInitialPairs] = useState([])

  const [invalidAttendance, setInvalidAttendance] = useState(false)

  const [editingDefaultDailyAccountTime, setEditingDefaultDailyAccountTime] = useState(false)

  const closeAccountSettingsModal = () => {
    setInvalidAttendance(false)
    onCancel()
  }

  const formatTime = (minutes) => {
    const hrs = Math.floor(minutes / 60)
    const mins = minutes % 60
    return hrs > 0 ? `${hrs} hrs ${mins} min` : `${mins} min`
  }

  const renderCloseButton = () => (
    <CloseButton
      placement="end"
      offset="small"
      onClick={closeAccountSettingsModal}
      screenReaderLabel="Close"
    />
  )

  useEffect(() => {
    setSubmitEnabled(false)

    // Check if attendance is invalid
    const notValidAttendance =
      // courses.some((c) => parseInt(c.default_time_to_complete, 10) > 1440) ||
      pairs.some((p) => parseInt(p.default_time_to_complete, 10) > 1440) ||
      parseInt(account.default_time_to_complete, 10) > 1440
    setInvalidAttendance(notValidAttendance)

    if (notValidAttendance) return

    if (
      // JSON.stringify(initialCourses) != JSON.stringify(courses) ||
      JSON.stringify(initialPairs) != JSON.stringify(pairs) ||
      JSON.stringify(initialAccountState) != JSON.stringify(account)
    ) {
      if (
        account.lockout_date_setting == 'specific_date' &&
        (account.lockout_date == '' || account.lockout_date == null)
      )
        return

      setSubmitEnabled(true)
    }
  }, [account, courses, pairs])

  // const bulkUpdateCourses = async () => {
  //   if (JSON.stringify(initialCourses) != JSON.stringify(courses)) {
  //     try {
  //       let params = {
  //         courses: courses.map((course) => ({
  //           canvas_id: course.canvas_id,
  //           default_time_to_complete: course.default_time_to_complete,
  //         })),
  //       }

  //       await API.bulkUpdateCourses(params)
  //     } catch (error) {
  //       console.error('Error updating courses:', error)
  //       throw error
  //     }
  //   }
  // }

  const updateDefaultAttendanceValue = async () => {
    if (JSON.stringify(initialPairs) != JSON.stringify(pairs)) {
      try {
        await API.bulkUpdatePairs({ pairs })
      } catch (error) {
        console.error('Error updating courses:', error)
        throw error
      }
    }
  }

  const handleSubmit = async () => {
    await updateAccountSettings(account)
    await updateDefaultAttendanceValue(account)
    // await bulkUpdateCourses(courses)
    setInitialAccountState(account)
    setInitialCourses(courses)
    setSubmitEnabled(false)
  }

  const renderLineBreak = () => (
    <hr
      style={{
        borderTop: 'dashed 1px',
        color: '#C6CDD0',
      }}
    />
  )

  const renderLockoutDateSetting = () => (
    <Flex direction="column">
      <Flex.Item padding="small">
        <Text size="large">Lockout Date Settings</Text>
      </Flex.Item>
      <Flex.Item padding="small">
        <RadioInputGroup
          layout="columns"
          name="lockout-date-settings"
          defaultValue={account.lockout_date_setting}
          description="Lockout Date By"
        >
          <Flex gap="medium" alignItems="start">
            <Flex.Item>
              <RadioInput
                key="days_after_course_end_date"
                value="days_after_course_end_date"
                label="Day Count From After the Course End Date"
                checked={account.lockout_date_setting == 'days_after_course_end_date'}
                themeOverride={{
                  borderWidth: '0.001rem',
                  borderColor: 'black',
                }}
                onClick={() =>
                  setAccount({ ...account, lockout_date_setting: 'days_after_course_end_date' })
                }
              />
              <div style={{ padding: '1rem 1.75rem' }}>
                <TextInput
                  width="5rem"
                  display="inline-block"
                  value={account.lockout_days.toString()}
                  interaction={
                    account.lockout_date_setting == 'days_after_course_end_date'
                      ? 'enabled'
                      : 'disabled'
                  }
                  maxLength={4}
                  onChange={(_event, value) => {
                    let lockoutDays = parseInt(value || 0).toString()

                    setAccount({ ...account, lockout_days: lockoutDays })
                  }}
                />
              </div>
            </Flex.Item>
            <Flex.Item>
              <RadioInput
                key="specific_date"
                value="specific_date"
                label="Specific Date"
                checked={account.lockout_date_setting == 'specific_date'}
                themeOverride={{
                  borderWidth: '0.001rem',
                  borderColor: 'black',
                }}
                onClick={() => setAccount({ ...account, lockout_date_setting: 'specific_date' })}
              />
              <div style={{ display: 'inline-block', padding: '1rem 1.75rem' }}>
                <DateInput2
                  renderLabel={false}
                  screenReaderLabels={{
                    calendarIcon: 'Calendar',
                    nextMonthButton: 'Next month',
                    prevMonthButton: 'Previous month',
                  }}
                  interaction={
                    account.lockout_date_setting == 'specific_date' ? 'enabled' : 'disabled'
                  }
                  value={
                    account.lockout_date
                      ? moment(account.lockout_date, 'DD-MM-YYYY').format('MM/DD/YYYY')
                      : ''
                  }
                  locale="en-us"
                  dateFormat={{
                    parser: (input) => {
                      const [month, day, year] = input.split(/[,.\s/.-]+/)
                      const newDate = new Date(year, month - 1, day)
                      return isNaN(newDate) ? '' : newDate
                    },
                    formatter: (date) => {
                      const year = date.getFullYear()
                      const month = `${date.getMonth() + 1}`.padStart(2, '0') // Month is zero-indexed
                      const day = `${date.getDate()}`.padStart(2, '0')
                      return `${month}/${day}/${year}` // Format as MM/DD/YYYY
                    },
                  }}
                  placeholder="MM/DD/YYYY"
                  onChange={(e, value) => {
                    setAccount({ ...account, lockout_date: moment(value).format('DD-MM-YYYY') })
                  }}
                />
              </div>
            </Flex.Item>
          </Flex>
        </RadioInputGroup>
      </Flex.Item>
      <Flex.Item padding="none large none">
        <Flex>
          <Flex.Item></Flex.Item>
        </Flex>
      </Flex.Item>
    </Flex>
  )

  // const renderSchoolDefaultTimeSetting = () => (
  //   <Flex direction="column" gap="small">
  //     <Flex.Item>
  //       <Text size="medium" weight="bold">
  //         Default Daily Attendance Time for School
  //       </Text>
  //     </Flex.Item>
  //     <Flex.Item padding="x-small">
  //       <TextInput
  //         renderLabel={false}
  //         value={
  //           editingDefaultDailyAccountTime
  //             ? `${account.default_time_to_complete}`
  //             : account.default_time_to_complete != null
  //               ? formatTime(account.default_time_to_complete)
  //               : ''
  //         }
  //         width="10rem"
  //         maxLength={4}
  //         textAlign="center"
  //         onFocus={(event) => {
  //           setEditingDefaultDailyAccountTime(true)

  //           requestAnimationFrame(() => {
  //             event.target.select()
  //           })
  //         }}
  //         themeOverride={
  //           parseInt(account.default_time_to_complete, 10) > 1440
  //             ? {
  //                 borderColor: '#C61F23',
  //               }
  //             : {}
  //         }
  //         onBlur={() => setEditingDefaultDailyAccountTime(false)}
  //         onChange={(_e, value) => {
  //           const newValue = (parseInt(value, 10) || '').toString()
  //           setAccount({ ...account, default_time_to_complete: newValue })
  //         }}
  //       />
  //     </Flex.Item>
  //   </Flex>
  // )

  return (
    <Modal
      open={open}
      onDismiss={closeAccountSettingsModal}
      size="fullscreen"
      width="100%"
      label="Account Settings"
      shouldCloseOnDocumentClick
    >
      <Modal.Header>
        {renderCloseButton()}
        <Heading level="h3">Account Settings</Heading>

        {invalidAttendance && (
          <Alert variant="warning">
            <Text weight="bold">Invalid:</Text> The student's daily total attendance value cannot
            exceed 1,440 minutes (24 hrs).
          </Alert>
        )}
      </Modal.Header>
      <Modal.Body>
        {renderLockoutDateSetting()}

        {renderLineBreak()}

        {/* {renderSchoolDefaultTimeSetting()} */}

        {/* <DefaultDailyAttendanceTable
          courses={courses}
          setCourses={setCourses}
          setInitialCourses={setInitialCourses}
        /> */}
        <DefaultDailyAttendanceTableV2
          pairs={pairs}
          setPairs={setPairs}
          setInitialPairs={setInitialPairs}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button color="primary-inverse" onClick={closeAccountSettingsModal}>
          Cancel
        </Button>
        <Button
          color="primary"
          margin="0 0 0 xx-small"
          onClick={handleSubmit}
          disabled={!submitEnabled}
          renderIcon={IconSaveLine}
        >
          Save
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default AccountSettingsModal
