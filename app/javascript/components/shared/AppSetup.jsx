import React, { useEffect, useState } from 'react'
import { Heading } from '@instructure/ui-heading'
import { Text } from '@instructure/ui-text'
import { Button } from '@instructure/ui'
import { Flex } from '@instructure/ui-flex'
import { IconSettingsLine } from '@instructure/ui-icons'
import { Table, View, Checkbox, Spinner, RadioInputGroup, RadioInput } from '@instructure/ui'

import AccountDisableModal from './AccountDisableModal'
import AccountSettingsModal from './AccountSettingsModal'

import * as API from '../../utils/api'

const AppSetup = ({ account, setAccount, loading, setLoading }) => {
  const [showAccountDisableModal, setShowAccountDisableModal] = useState(false)
  const [showAccountSettings, setShowAccountSettings] = useState(false)

  const hideAccountDisableModal = () => setShowAccountDisableModal(false)
  const hideAccountSettings = () => setShowAccountSettings(false)

  useEffect(() => {
    hideAccountDisableModal()
  }, [])

  const handleAccountEnableChange = async (accountEnabled, options = { confirmFlag: false }) => {
    if (account.enabled == accountEnabled) return

    if (!accountEnabled && !options.confirmFlag) {
      setShowAccountDisableModal(true)
      return
    }

    const updatedAccount = {
      ...account,
      enabled: accountEnabled,
    }

    setAccount(updatedAccount)

    await updateAccountSettings(updatedAccount)
  }

  const updateAccountSettings = async (updatedAccount) => {
    await API.updateAccount(updatedAccount.canvas_id, { account: updatedAccount })
      .then((response) => {
        setAccount(response.data)
        setLoading(false)
      })
      .catch(function (error) {
        console.error('Error updating account:', error)
        throw error
      })
  }

  if (loading) {
    return (
      <Flex alignItems="center" justifyItems="center" padding="xx-large">
        <Spinner renderTitle="Loading students..." />
      </Flex>
    )
  }

  return (
    <>
      <Flex alignItems="start" margin="medium none">
        <Flex.Item shouldGrow>
          <Flex direction="column">
            <Flex.Item>
              <Heading level="h3">School Administrator Account</Heading>
            </Flex.Item>
            <Flex.Item margin="small none">
              <Text>Application Setup and Configurations</Text>
            </Flex.Item>
          </Flex>
        </Flex.Item>
        <Flex.Item>
          <View></View>
          <Button onClick={() => setShowAccountSettings(true)} disabled={!account.enabled}>
            <IconSettingsLine /> Account Settings
          </Button>
        </Flex.Item>
      </Flex>

      <div style={{ width: '70%' }}>
        <Table caption={false}>
          <Table.Head>
            <Table.Row>
              <Table.ColHeader id="application">Application</Table.ColHeader>
              <Table.ColHeader id="none"></Table.ColHeader>
            </Table.Row>
          </Table.Head>
          <Table.Body>
            <Table.Row>
              <Table.Cell textAlign="start">Learning Coach Attendance</Table.Cell>
              <Table.Cell textAlign="end">
                <RadioInputGroup
                  name="application-configuration"
                  defaultValue={account.enabled ? 'on' : 'off'}
                  value={account.enabled ? 'on' : 'off'}
                  description={false}
                  variant="toggle"
                  size="small"
                >
                  <RadioInput
                    label="Off"
                    value="off"
                    context="off"
                    themeOverride={{
                      toggleBackgroundOff: '#73818C',
                    }}
                    onClick={() => handleAccountEnableChange(false)}
                  />
                  <RadioInput
                    label="On"
                    value="on"
                    onClick={() => handleAccountEnableChange(true)}
                  />
                </RadioInputGroup>
              </Table.Cell>
            </Table.Row>
          </Table.Body>
        </Table>
      </div>

      <AccountDisableModal
        open={showAccountDisableModal}
        onCancel={() => {
          setAccount({ ...account, enabled: true })
          hideAccountDisableModal()
        }}
        onSubmit={() => {
          handleAccountEnableChange(false, { confirmFlag: true })
          hideAccountDisableModal()
        }}
      />

      <AccountSettingsModal
        open={showAccountSettings}
        onCancel={hideAccountSettings}
        account={account}
        setAccount={setAccount}
        updateAccountSettings={updateAccountSettings}
      />
    </>
  )
}
export default AppSetup
