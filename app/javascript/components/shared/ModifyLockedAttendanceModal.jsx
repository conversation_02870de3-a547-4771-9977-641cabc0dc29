import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from '@instructure/ui'

const ModifyLockedAttendanceModal = ({ open, setShowModifyLockedAttendanceModal }) => {
  return (
    <Modal open={open} size="small" label="Attendance Locked Modal">
      <Modal.Header>
        <Heading>Course Locked</Heading>
      </Modal.Header>
      <Modal.Body>Are your sure, you want to modify attendance for Locked Course?</Modal.Body>
      <Modal.Footer>
        <Button
          color="primary"
          margin="0 0 0 xx-small"
          onClick={() => setShowModifyLockedAttendanceModal(false)}
        >
          Confirm
        </Button>
      </Modal.Footer>
    </Modal>
  )
}

export default ModifyLockedAttendanceModal
