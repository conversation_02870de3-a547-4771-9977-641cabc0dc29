import React, { useState, useEffect } from 'react'
import { View, Flex, Text, Link, Button } from '@instructure/ui'

import { IconExportLine } from '@instructure/ui-icons'

import StudentsTable from './StudentsTable'
import SelectStudent from './SelectStudent'
import MissingAttendanceTray from './../attendance/MissingAttendanceTray'
import StudentAttendance from './../attendance/StudentAttendance'
import ExportReportTray from '../attendance/ExportReportTray'

import * as API from './../../utils/api'

const STUDENTS_PER_PAGE = 25

const StudentsList = () => {
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState('users.sortable_name')
  const [ascending, setAscending] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)

  const [totalStudents, setTotalStudents] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  const [students, setStudents] = useState([])
  const [missingAttendanceCount, setMissingAttendanceCount] = useState(0)
  const [selectedStudentId, setSelectedStudentId] = useState(null)
  const [selectedDate, setSelectedDate] = useState(null)
  const [isMissingAttendanceTrayOpen, setIsMissingAttendanceTrayOpen] = useState(false)

  const [isExportTrayOpen, setIsExportTrayOpen] = useState(false)

  const handleShowMissingAttendanceAlerts = async () => {
    setIsMissingAttendanceTrayOpen(true)
  }

  const fetchMissingAttendanceCount = async (params = {}) => {
    await API.getMissingAttendance(params)
      .then((response) => {
        setMissingAttendanceCount(response.data.missing_attendance_count)
      })
      .catch((error) => {
        console.error('Error fetching missing attendance count:', error)
      })
  }

  useEffect(() => {
    fetchMissingAttendanceCount()
  }, [])

  const handleStudentClick = (id, date = null) => {
    if (date) setSelectedDate(date)
    setSelectedStudentId(id)
    setIsMissingAttendanceTrayOpen(false)
  }

  const handleBreadCrumbClick = () => {
    setSelectedStudentId(null)
  }

  const handleStudentTrayClick = (date, id) => {
    handleStudentClick(id, date)
  }

  useEffect(() => {
    const fetchStudents = async () => {
      await API.getUsers({
        per_page: STUDENTS_PER_PAGE,
        page: currentPage,
        sort_by: sortBy,
        sort_order: ascending ? 'ASC' : 'DESC',
      })
        .then((response) => response.data)
        .then((data) => {
          setStudents(data.users)
          setTotalStudents(data.pagination.total_items)
          setTotalPages(data.pagination.total_pages)
          setLoading(false)
        })
        .catch((error) => {
          setLoading(false)
          console.error('Error fetching students:', error)
        })
    }

    fetchStudents()
  }, [currentPage, sortBy, ascending])

  const handleSort = (column) => {
    if (sortBy === column) {
      setAscending(!ascending)
    } else {
      setSortBy(column)
      setAscending(true)
    }
  }

  return (
    <View>
      {selectedStudentId ? (
        <StudentAttendance
          studentId={selectedStudentId}
          onBack={handleBreadCrumbClick}
          selectedDate={selectedDate}
        />
      ) : (
        <>
          <Flex justifyItems="space-between">
            <Flex.Item shouldGrow>
              <Text>All Students</Text>
            </Flex.Item>
            <Flex.Item align="end" padding="small">
              <Text weight="bold">
                Missing Attendance (All Students){' '}
                <Link
                  href="#"
                  themeOverride={{
                    focusOutlineWidth: 0,
                  }}
                  onClick={() => handleShowMissingAttendanceAlerts()}
                >
                  {missingAttendanceCount} Alerts
                </Link>
              </Text>
            </Flex.Item>
          </Flex>

          <Text size="large" weight="bold">
            Student Attendance
          </Text>

          <Flex justifyItems="space-between">
            <Flex.Item shouldGrow>
              <Text size="small">
                {totalStudents} Students {totalPages > 1 && `on ${totalPages} Pages`}
              </Text>
            </Flex.Item>
            {(window.ENV.user_is_stride_admin || window.ENV.user_is_school_admin) && (
              <Flex.Item align="end">
                <Button renderIcon={IconExportLine} onClick={() => setIsExportTrayOpen(true)}>
                  Report
                </Button>
              </Flex.Item>
            )}
          </Flex>

          <Flex padding="none 0 medium 0">
            <Flex.Item width="24rem">
              <SelectStudent onStudentClick={handleStudentClick} />
            </Flex.Item>
          </Flex>

          <StudentsTable
            students={students}
            onStudentClick={handleStudentClick}
            loading={loading}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            totalPages={totalPages}
            sortBy={sortBy}
            ascending={ascending}
            handleSort={handleSort}
          />

          <MissingAttendanceTray
            setOpen={setIsMissingAttendanceTrayOpen}
            open={isMissingAttendanceTrayOpen}
            handleClick={handleStudentTrayClick}
          />

          <ExportReportTray open={isExportTrayOpen} setOpen={setIsExportTrayOpen} />
        </>
      )}
    </View>
  )
}

export default StudentsList
