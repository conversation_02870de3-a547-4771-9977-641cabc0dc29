import React from 'react'
import { Flex } from '@instructure/ui-flex'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import { IconDocumentLine } from '@instructure/ui-icons'
import { FileDrop } from '@instructure/ui-file-drop'
import { IconEndLine } from '@instructure/ui-icons'
import { IconButton } from '@instructure/ui-buttons'

const FileUpload = (props) => {
  const { importFile, setImportFile } = props

  const handleImportFileClose = () => {
    setImportFile(null)
  }

  return (
    <Flex as="div" direction="column" padding="xxx-small 0 0 large" justifyItems="center" margin="xx-small large 0 xx-large">
      <Flex.Item padding="xx-small 0 small xx-large" margin="0 x-small x-small xx-large">
        <FileDrop
          accept=".csv"
          onDropAccepted={([file]) => {
            setImportFile(file)
          }}
          onDropRejected={([file]) => {
            console.log(`File rejected ${file.name}`)
          }}
          renderLabel={
            <View as="div" textAlign="center" margin="x-small 0" padding="x-small medium">
              <View as="div" padding="0 0 x-small 0" margin="0 0 x-small 0">
                <IconDocumentLine color="secondary" width="40px" height="40px" />
              </View>
              <Text as="div"> Upload CSV File </Text>
              <Text size="x-small" as="div" lineHeight="condensed">
                .csv file only
              </Text>
            </View>
          }
          display="inline-block"
          width="43%"
        />
      </Flex.Item>
      {importFile && importFile.name && (
        <Flex.Item padding="x-small small x-small xx-small">
          <Flex direction="row" padding="0 xxx-small 0 xxx-small">
            <Flex.Item padding="0 small 0 xxx-small">
              <View className="word-wrap-break">
                <Text size="small">{importFile.name}</Text>
              </View>
              <IconButton
                withBackground={false}
                withBorder={false}
                screenReaderLabel="Remove Image"
                size="small"
                themeOverride={{
                  secondaryGhostHoverBackground: 'secondary',
                  iconSizeSmall: '1.06rem',
                }}
                onClick={(event) => handleImportFileClose()}
              >
                <IconEndLine color="brand" />
              </IconButton>
            </Flex.Item>
          </Flex>
        </Flex.Item>
      )}
    </Flex>
  )
}

export default FileUpload
