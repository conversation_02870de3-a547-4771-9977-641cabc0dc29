import React, { useState, useCallback } from 'react'
import { IconSearchLine, IconUserLine } from '@instructure/ui-icons'
import { Select } from '@instructure/ui-select'
import debounce from '@instructure/debounce'
import { Flex } from '@instructure/ui-flex'
import { Text } from '@instructure/ui-text'

import { searchUsers } from './../../utils/api'

const SelectStudent = ({ onStudentClick }) => {
  const [inputValue, setInputValue] = useState('')
  const [isShowingOptions, setIsShowingOptions] = useState(false)
  const [filteredOptions, setFilteredOptions] = useState([])
  const [loading, setLoading] = useState(false)
  const [highlightedOptionId, setHighlightedOptionId] = useState(null)

  const getOptionById = (queryId) => {
    return filteredOptions.find(({ id }) => id.toString() === queryId)
  }

  const fetchOptions = useCallback(
    debounce(async (query) => {
      if (query.length < 3) return

      setLoading(true)
      try {
        const response = await searchUsers({ search: query })
        const users = response.data.data.map((user) => ({
          id: user.canvas_id,
          label: user.sortable_name,
          sisId: user.sis_id,
        }))
        setFilteredOptions(users)
      } catch (error) {
        console.error('Failed to fetch students:', error)
      } finally {
        setLoading(false)
      }
    }, 500),
    [],
  )

  const handleInputChange = (event) => {
    const value = event.target.value
    setInputValue(value)
    setFilteredOptions([])
    setIsShowingOptions(true)

    if (value.trim() !== '') {
      fetchOptions(value)
    } else {
      setFilteredOptions([])
    }
  }

  const handleSelectOption = (event, { id }) => {
    const option = getOptionById(id)
    if (!option) return

    onStudentClick(id)
    setIsShowingOptions(false)
  }

  const handleHighlightOption = (event, { id }) => {
    setHighlightedOptionId(id)
  }

  const handleShowOptions = () => {
    setIsShowingOptions(true)
  }

  const handleHideOptions = () => {
    setInputValue('')
    setHighlightedOptionId(null)
    setIsShowingOptions(false)
  }

  return (
    <Select
      renderLabel={false}
      assistiveText="Search and select a student."
      placeholder="Start typing to search by student name..."
      inputValue={inputValue}
      isShowingOptions={isShowingOptions}
      onInputChange={handleInputChange}
      onRequestShowOptions={handleShowOptions}
      onRequestHideOptions={handleHideOptions}
      onRequestSelectOption={handleSelectOption}
      onRequestHighlightOption={handleHighlightOption}
      renderBeforeInput={<IconUserLine inline={false} />}
      renderAfterInput={<IconSearchLine inline={false} />}
    >
      {loading ? (
        <Select.Option id="loading" key="loading">
          Loading students...
        </Select.Option>
      ) : filteredOptions.length > 0 ? (
        filteredOptions.map((option) => (
          <Select.Option
            id={option.id.toString()}
            key={option.id.toString()}
            isHighlighted={option.id.toString() === highlightedOptionId}
          >
            <Flex alignItems="start">
              <Flex.Item margin="none small none none">
                <IconUserLine />
              </Flex.Item>
              <Flex.Item>
                <Flex direction="column">
                  <Flex.Item>{option.label}</Flex.Item>
                  <Flex.Item>
                    <Text size="x-small">ID: {option.sisId}</Text>
                  </Flex.Item>
                </Flex>
              </Flex.Item>
            </Flex>
          </Select.Option>
        ))
      ) : (
        <Select.Option id="empty-option" key="empty-option">
          No students found.
        </Select.Option>
      )}
    </Select>
  )
}

export default SelectStudent
