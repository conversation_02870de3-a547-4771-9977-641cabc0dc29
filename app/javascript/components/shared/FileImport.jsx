import React from 'react'
import { Flex } from '@instructure/ui-flex'
import * as API from '../../utils/api'
import { View } from '@instructure/ui-view'
import { CloseButton, Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { useState, useEffect } from 'react'
import { Spinner } from '@instructure/ui-spinner'
import FileUpload from './FileUpload'
import { Heading } from '@instructure/ui-heading'
import { Link } from '@instructure/ui'

const FileImport = (props) => {
  const [file, setFile] = useState()
  const [lastImport, setLastImport] = useState({})
  const [importState, setImportState] = useState({
    importId: null,
    started: false,
    status: '',
    pollHandler: null,
    completedAt: null,
  })

  useEffect(() => {
    API.latestImport()
      .then((resp) => {
        setLastImport(resp.data)
      })
      .catch((error) => {
        console.log(error)
      })
  }, [])

  useEffect(() => {
    if (importState.pollHandler && !importState.started) {
      clearInterval(importState.pollHandler)
    }
  }, [importState.started])

  const startPolling = async (importId) => {
    let response = null
    try {
      response = await API.getImport(importId)
      switch (response.data['status']) {
        case 'Complete':
        case 'Complete With Errors':
        case 'Failed':
          setImportState((prevState) => ({
            ...prevState,
            started: false,
            status: response.data['status'],
            importId: null,
            completedAt: response.data['completed_at'],
          }))
          setLastImport(response.data)
          setFile(undefined)
          setLoading(false)
          break
        default:
          break
      }
    } catch (err) {
      setImportState((prevState) => ({
        ...prevState,
        started: false,
        status: 'error',
        jobId: null,
        completedAt: null,
      }))
      setLoading(false)
      setFile(undefined)
    }
  }

  const [loading, setLoading] = useState()

  useEffect(() => {
    if (file) {
      setLoading(true)
      API.fileImport({ 'import[file]': file })
        .then((resp) => {
          let data = resp.data
          let handler = null
          handler = setInterval(() => startPolling(data['id']), 4000)
          setImportState((prevState) => ({
            ...prevState,
            started: true,
            status: data['status'],
            importId: data['id'],
            pollHandler: handler,
          }))
        })
        .catch((error) => {
          console.log(error)
        })
    }
  }, [file])

  const renderSpinner = () => {
    return (
      <Flex direction="column" textAlign="center" padding="medium 0 0 0">
        <Flex.Item>
          <Spinner
            renderTitle="Loading"
            size="small"
            themeOverride={{
              smallBorderWidth: '0.25em',
              color: '#4589ff',
            }}
          />
        </Flex.Item>
        <Flex.Item padding="medium 0">
          <Text>File import is in Progress...</Text>
        </Flex.Item>
      </Flex>
    )
  }

  return (
    <>
      {loading ? (
        renderSpinner()
      ) : (
        <Flex direction="column" shouldShrink shouldGrow margin="xx-small large 0 x-large">
          <Flex.Item>

            <Heading
              level="h4"
              size="large"
              margin="xx-small xx-large 0 xx-large"
            >
              Upload CSV file with Grade and Subject pairs to bulk update the default attendance values
            </Heading>

            <View display="inline-block" margin="xx-small medium x-small xx-large">
              <Text
                color="secondary"
                size='small'
              >
                Click <Link href="/sample_bulk_upload.csv" download>
                        here
                      </Link> to get Sample CSV file
              </Text>
            </View>
          </Flex.Item>

          <Flex.Item>
            <FileUpload importFile={file} setImportFile={setFile} />
          </Flex.Item>

          {lastImport.id && (
            <Flex.Item>
              <Flex direction="column" margin="xx-small medium 0 xx-large">
                <Flex.Item>
                  <Text
                    color="secondary"
                    themeOverride={{
                      secondaryColor: '#394B58',
                    }}
                  >
                    <b>Last Import</b>
                  </Text>
                </Flex.Item>
                <Flex.Item padding="x-small 0 xxx-small 0">
                  <Text> Initiated: {lastImport.initiated_at} </Text>
                </Flex.Item>
                <Flex.Item>
                  <Text> Status: {lastImport.status} </Text>
                </Flex.Item>
                {Object.keys(lastImport.error_records).length && (
                  <Flex.Item>
                    <br></br>
                    <b>First 10 errors</b>
                    {Object.entries(lastImport.error_records)
                      .slice(0, 10)
                      .map(([key, value]) => (
                        <div key={key}>
                          <strong>{key}:</strong> {value}
                        </div>
                      ))}
                  </Flex.Item>
                )}
              </Flex>
            </Flex.Item>
          )}
        </Flex>
      )}
    </>
  )
}

export default FileImport
