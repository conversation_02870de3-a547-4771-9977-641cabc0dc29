import React, { useState } from 'react'
import { View, Tabs } from '@instructure/ui'
import Header from './shared/Header'

import StudentsList from './shared/StudentsList'

const SchoolAdminDashboard = () => {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const handleTabChange = (_event, { index }) => {
    setSelectedIndex(index)
  }

  return (
    <View>
      <Header />

      <Tabs onRequestTabChange={handleTabChange}>
        <Tabs.Panel
          id="students"
          renderTitle="Students"
          isSelected={selectedIndex === 0}
        >
          <StudentsList />
        </Tabs.Panel>
      </Tabs>
    </View>
  )
}

export default SchoolAdminDashboard
