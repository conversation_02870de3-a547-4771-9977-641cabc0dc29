import React, { useState, useEffect } from 'react'
import { View, Tabs } from '@instructure/ui'
import Header from './shared/Header'

import StudentsList from './shared/StudentsList'
import FileImport from './shared/FileImport'
import AppSetup from './shared/AppSetup'

import * as API from '../utils/api'

const SchoolAdminDashboard = () => {
  const [selectedIndex, setSelectedIndex] = useState(0)

  const [account, setAccount] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    API.getAccount(window.ENV.current_account.canvas_id)
      .then((response) => {
        setAccount(response.data)
        setLoading(false)
      })
      .catch((error) => {
        console.error('Error fetching account details:', error)
      })
  }, [])

  const handleTabChange = (_event, { index }) => {
    setSelectedIndex(index)
  }

  return (
    <View>
      <Header />

      <Tabs onRequestTabChange={handleTabChange}>
        <Tabs.Panel id="students" renderTitle="Students" isSelected={selectedIndex === 0}>
          <StudentsList />
        </Tabs.Panel>

        <Tabs.Panel id="administrator" renderTitle="Administrator" isSelected={selectedIndex === 1}>
          <AppSetup
            account={account}
            setAccount={setAccount}
            loading={loading}
            setLoading={setLoading}
          />
        </Tabs.Panel>

        {(window.ENV.launch_point == 'account_navigation' && !account?.canvas_parent_account_id) && <Tabs.Panel
          id="bulkCsvImport"
          renderTitle="Bulk Csv Import"
          isSelected={selectedIndex === 2}
        >
          <FileImport />
        </Tabs.Panel>}
      </Tabs>
    </View>
  )
}

export default SchoolAdminDashboard
