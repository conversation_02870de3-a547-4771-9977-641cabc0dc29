import React from 'react'
import { Flex } from '@instructure/ui-flex'
import { Alert } from '@instructure/ui-alerts'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import Header from './shared/Header'

const UnAuthorized = () => {
  return (
    <View>
      <Header />
      <Flex padding="0 xx-large">
        <Flex.Item shouldGrow>
          <div style={{ padding: '0 10rem' }}>
            <Alert variant="error">
              <Text weight="bold">No Access:</Text> This application is not authorized for this
              account.
            </Alert>
          </div>
        </Flex.Item>
      </Flex>
    </View>
  )
}

export default UnAuthorized
