import React, { useState } from 'react'
import { Flex } from '@instructure/ui-flex'
import { View } from '@instructure/ui-view'
import { Table, TextInput } from '@instructure/ui'
import { IconCoursesLine } from '@instructure/ui-icons'
import { Text } from '@instructure/ui-text'
import { IconWarningLine } from '@instructure/ui-icons'
import { Tooltip } from '@instructure/ui-tooltip'
import { Pill } from '@instructure/ui-pill'

import ModifyLockedAttendanceModal from '../shared/ModifyLockedAttendanceModal'

const CourseTimeTable = ({ courses, onCoursesUpdate, disableInput }) => {
  const [editingCourseId, setEditingCourseId] = useState(null)
  const [showModifyLockedAttendanceModal, setShowModifyLockedAttendanceModal] = useState(false)
  const [modifyLockedAttendanceModalShown, setModifyLockedAttendanceModalShown] = useState(false)

  const formatTime = (minutes) => {
    const hrs = Math.floor(minutes / 60)
    const mins = minutes % 60
    return hrs > 0 ? `${hrs} hrs ${mins} min` : `${mins} min`
  }

  const calculateTotalTime = (key) =>
    courses
      .filter((course) => course[key] != null)
      .reduce((sum, course) => sum + parseInt(course[key]), 0)

  const handleTimeChange = (courseId, newTimeStr) => {
    const newTime = (parseInt(newTimeStr, 10) || 0).toString()
    const updatedCourses = courses.map((course) =>
      course.canvas_id === courseId ? { ...course, time_in_minutes: newTime } : course,
    )
    onCoursesUpdate(updatedCourses)
  }

  const renderTextInputWithTooltip = (course) => (
    <Tooltip
      renderTip={
        <>
          {course.disable_attendance_recording
            ? "Attendance data cannot be added for the course for this date, due to it falling outside of the course's start and end dates."
            : 'Attendance values are locked and can no longer be edited.'}
        </>
      }
      offsetX="5px"
    >
      {renderTextInput(course)}
    </Tooltip>
  )

  const renderTextInput = (course) => (
    <TextInput
      disabled={
        (course.locked && !window.ENV.user_is_stride_admin) || course.disable_attendance_recording
      }
      interaction={
        (course.locked && !window.ENV.user_is_stride_admin) || course.disable_attendance_recording
          ? 'readonly'
          : 'enabled'
      }
      renderLabel={false}
      value={
        editingCourseId === course.canvas_id
          ? `${course.time_in_minutes}`
          : course.time_in_minutes != null
            ? formatTime(course.time_in_minutes)
            : ''
      }
      maxLength={4}
      textAlign="center"
      onFocus={(event) => {
        if (
          (course.locked && !window.ENV.user_is_stride_admin) ||
          course.disable_attendance_recording
        )
          return

        if (course.locked && !modifyLockedAttendanceModalShown) {
          setModifyLockedAttendanceModalShown(true)
          setShowModifyLockedAttendanceModal(true)
        }

        if (course.time_in_minutes == null)
          handleTimeChange(course.canvas_id, course.default_time_to_complete)
        setEditingCourseId(course.canvas_id)

        requestAnimationFrame(() => {
          event.target.select()
        })
      }}
      onBlur={() => setEditingCourseId(null)}
      onChange={(_e, value) => handleTimeChange(course.canvas_id, value)}
    />
  )

  const renderDisabledTextInput = (course) => (
    <TextInput
      disabled={true}
      interaction={'readonly'}
      renderLabel={false}
      value={''}
      maxLength={4}
      textAlign="center"
    />
  )

  const renderCourses = () =>
    courses.map((course) => (
      <Table.Row key={course.canvas_id}>
        <Table.Cell>
          <Flex justifyItems="start">
            <Flex.Item>
              <Flex justifyItems="space-around" direction="column">
                <Flex.Item><IconCoursesLine />&nbsp; {course.name}</Flex.Item>
                {(course.resolved_start_date || course.resolved_end_date) && (
                  <Flex.Item margin="0 medium 0">
                    <Text size="x-small">
                      {course.resolved_start_date &&
                        `Start Date: ${new Date(course.resolved_start_date).toLocaleDateString().replace(/\//g, '-')}`}
                      {course.resolved_start_date && course.resolved_end_date && ' | '}
                      {course.resolved_end_date &&
                        `End Date: ${new Date(course.resolved_end_date).toLocaleDateString().replace(/\//g, '-')}`}
                    </Text>
                  </Flex.Item>
                )}
              </Flex>
            </Flex.Item>
            {(course.locked || course.disable_attendance_recording) && (
              <Flex.Item margin="small">
                <Pill>Locked</Pill>
              </Flex.Item>
            )}
          </Flex>
        </Table.Cell>
        <Table.Cell textAlign="center">{formatTime(course.default_time_to_complete)}</Table.Cell>
        <Table.Cell
          textAlign="center"
          themeOverride={{ padding: 0, background: disableInput ? '#F4F4F5' : '#EBF4F8' }}
        >
          <Flex justifyItems="space-around">
            <Flex.Item padding="small">
              {disableInput
                ? renderDisabledTextInput(course)
                : (course.locked && !window.ENV.user_is_stride_admin) ||
                    course.disable_attendance_recording
                  ? renderTextInputWithTooltip(course)
                  : renderTextInput(course)}
            </Flex.Item>
          </Flex>
        </Table.Cell>
        <Table.Cell textAlign="center">{formatTime(course.total_time_in_minutes)}</Table.Cell>
      </Table.Row>
    ))

  const renderTotalsRow = () => (
    <Table.Row key="totals">
      <Table.Cell></Table.Cell>
      <Table.Cell textAlign="center">
        <Text size="large">Totals</Text>
      </Table.Cell>
      <Table.Cell textAlign="center">
        <View>
          <Flex alignItems="center" direction="column">
            <Flex.Item shouldGrow>
              <Text size="large">
                {courses.some((course) => course.time_in_minutes != null)
                  ? formatTime(calculateTotalTime('time_in_minutes'))
                  : '--'}
              </Text>
            </Flex.Item>
            <Flex.Item>
              {calculateTotalTime('time_in_minutes') > 1440 && (
                <>
                  <IconWarningLine size="x-small" />{' '}
                </>
              )}
              <Text size="small">Daily Total</Text>
            </Flex.Item>
          </Flex>
        </View>
      </Table.Cell>
      <Table.Cell textAlign="center">
        <Flex alignItems="center" direction="column">
          <Flex.Item>
            <Text size="large">{formatTime(calculateTotalTime('total_time_in_minutes'))}</Text>
          </Flex.Item>
          <Flex.Item>
            <Text size="small">Cumulative Total</Text>
          </Flex.Item>
        </Flex>
      </Table.Cell>
    </Table.Row>
  )

  return (
    <>
      <Table caption="Courses Table" layout="auto" hover>
        <Table.Head>
          <Table.Row>
            <Table.ColHeader id="course">Course</Table.ColHeader>
            <Table.ColHeader id="default-time-to-complete" textAlign="center">
              Default Time to Complete
            </Table.ColHeader>
            <Table.ColHeader id="time-to-complete" textAlign="center">
              Time to Complete
            </Table.ColHeader>
            <Table.ColHeader id="totals-to-date" textAlign="center">
              Totals To Date
            </Table.ColHeader>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {renderCourses()}
          {renderTotalsRow()}
        </Table.Body>
      </Table>

      {/* Modify Locked Attendance Confirmation Modal */}
      <ModifyLockedAttendanceModal
        open={showModifyLockedAttendanceModal}
        setShowModifyLockedAttendanceModal={setShowModifyLockedAttendanceModal}
      />
    </>
  )
}

export default CourseTimeTable
