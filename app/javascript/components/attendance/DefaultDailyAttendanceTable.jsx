// TODO: Deprecate this in favour of Subject & Grade Pairs

import React, { useState, useEffect } from 'react'
import { View, Table, Text, Flex, Spinner } from '@instructure/ui'
import { TextInput } from '@instructure/ui-text-input'
import { IconBlueprintLine } from '@instructure/ui-icons'
import { Pagination } from '@instructure/ui-pagination'

import * as API from './../../utils/api'

const COURSES_PER_PAGE = 25

const DefaultDailyAttendanceTable = ({ courses, setCourses, setInitialCourses }) => {
  const [loading, setLoading] = useState(true)

  const [editingCourseId, setEditingCourseId] = useState(null)

  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCourses, setTotalCourses] = useState(0)

  const fetchBlueprintCourses = async () => {
    setLoading(true)

    await API.getBlueprintCourses({
      per_page: COURSES_PER_PAGE,
      page: currentPage,
    })
      .then((response) => response.data)
      .then((data) => {
        setCourses(data.courses)
        setInitialCourses(structuredClone(data.courses))
        setTotalCourses(data.pagination.total_items)
        setTotalPages(data.pagination.total_pages)
        setLoading(false)
      })
      .catch((error) => {
        console.error('Error fetching blueprint courses:', error)
      })
  }

  useEffect(() => {
    const fetchData = async () => {
      await fetchBlueprintCourses()
    }

    fetchData()
  }, [currentPage])

  if (loading) {
    return (
      <Flex alignItems="center" justifyItems="center" padding="xx-large">
        <Spinner renderTitle="Loading courses..." />
      </Flex>
    )
  }

  const renderTableHeaders = () => (
    <Table.Row>
      <Table.ColHeader id="course-name" width="70%">
        Blueprint Course
      </Table.ColHeader>
      <Table.ColHeader id="daily-time-to-complete" textAlign="center">
        Default Daily Time to Complete
      </Table.ColHeader>
    </Table.Row>
  )

  const renderTableBody = () =>
    courses.map((course, index) => (
      <Table.Row key={course.canvas_id}>
        <Table.Cell>
          <IconBlueprintLine />
          &ensp;{course.name}
        </Table.Cell>
        <Table.Cell textAlign="center" themeOverride={{ padding: '0.6rem 0 0 0' }}>
          <Flex justifyItems="space-around">
            <Flex.Item padding="none none small" width="9rem">
              {renderTextInput(course)}
            </Flex.Item>
          </Flex>
        </Table.Cell>
      </Table.Row>
    ))

  const renderTextInput = (course) => (
    <TextInput
      renderLabel={false}
      value={
        editingCourseId === course.canvas_id
          ? `${course.default_time_to_complete}`
          : course.default_time_to_complete != null
            ? formatTime(course.default_time_to_complete)
            : ''
      }
      maxLength={4}
      textAlign="center"
      onFocus={(event) => {
        setEditingCourseId(course.canvas_id)

        requestAnimationFrame(() => {
          event.target.select()
        })
      }}
      themeOverride={
        course.default_time_to_complete > 1440
          ? {
              borderColor: '#C61F23',
            }
          : {}
      }
      onBlur={() => setEditingCourseId(null)}
      onChange={(_e, value) => handleTimeChange(course.canvas_id, value)}
    />
  )

  const formatTime = (minutes) => {
    const hrs = Math.floor(minutes / 60)
    const mins = minutes % 60
    return hrs > 0 ? `${hrs} hrs ${mins} min` : `${mins} min`
  }

  const handleTimeChange = (courseId, newTimeStr) => {
    const newTime = (parseInt(newTimeStr, 10) || 0).toString()

    const updatedCourses = courses.map((course) =>
      course.canvas_id === courseId ? { ...course, default_time_to_complete: newTime } : course,
    )

    setCourses(updatedCourses)
  }

  return (
    <View margin="small">
      <Flex direction="column" gap="medium">
        <Flex.Item>
          <Text size="large">Set Default Daily Attendance Values</Text>
        </Flex.Item>
        <Flex.Item>
          <Text size="small">
            {totalCourses} Courses {totalPages > 1 && `on ${totalPages} Pages`}
          </Text>
        </Flex.Item>
        <Flex.Item>
          <Table caption={false} layout="fixed" hover>
            <Table.Head>{renderTableHeaders()}</Table.Head>
            <Table.Body>{renderTableBody()}</Table.Body>
          </Table>

          {totalPages > 1 && (
            <Pagination
              as="nav"
              margin="large small small"
              variant="compact"
              labelNext="Next Page"
              labelPrev="Previous Page"
              labelFirst="First Page"
              labelLast="Last Page"
              withFirstAndLastButton
              currentPage={currentPage}
              onPageChange={(nextPage) => setCurrentPage(nextPage)}
              totalPageNumber={totalPages}
              siblingCount={3}
              boundaryCount={2}
            />
          )}
        </Flex.Item>
      </Flex>
    </View>
  )
}

export default DefaultDailyAttendanceTable
