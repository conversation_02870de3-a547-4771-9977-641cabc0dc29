import React, { useState } from 'react'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { View } from '@instructure/ui-view'
import { CloseButton } from '@instructure/ui-buttons'
import { Tray } from '@instructure/ui-tray'
import { Text } from '@instructure/ui-text'
import { IconUserLine, IconLockLine } from '@instructure/ui-icons'
import { Table } from '@instructure/ui-table'
import { Link } from '@instructure/ui-link'

import moment from 'moment'
import * as API from '../../utils/api'
import { Pill } from '@instructure/ui'

const MissingAttendanceTray = ({ open, setOpen, student, handleClick }) => {
  const [loading, setLoading] = useState(true)
  const [missingAttendances, setMissingAttendances] = useState([])
  const [missingAttendanceCount, setMissingAttendanceCount] = useState(0)

  const formatDate = (date) => {
    const formattedDate = moment(date, 'DD-MM-YYYY')

    return formattedDate.format('dddd, MMMM DD, YYYY')
  }

  const hideTray = () => {
    setLoading(true)
    setMissingAttendanceCount(0)
    setMissingAttendances([])
    setOpen(false)
  }

  const isCourseLocked = (course_end_date) => {
    // TODO: Deprecated (Currently we can have courses for a day with a mixed combination of locked & active statuses)
    return false

    if (window.ENV.lockout_date_setting == 'days_after_course_end_date') {
      const course_lock_date = moment(course_end_date, 'DD-MM-YYYY').add({
        days: window.ENV.lockout_days,
      })

      return moment().isAfter(course_lock_date, 'day')
    } else {
      return moment().isAfter(window.ENV.lockout_date, 'day')
    }
  }

  const fetchMissingAttendance = async () => {
    setLoading(true)
    try {
      const response = await API.getMissingAttendance({ user_id: student?.canvas_id })
      setMissingAttendanceCount(response.data.missing_attendance_count)
      setMissingAttendances(response.data.data)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching missing attendance:', error)
    }
  }

  const renderHeader = () => (
    <Flex>
      <Flex.Item shouldGrow shouldShrink>
        <Heading level="h3">Missing Attendance Alerts</Heading>
      </Flex.Item>
      <Flex.Item>
        <CloseButton
          placement="end"
          offset="small"
          screenReaderLabel="Close"
          onClick={() => setOpen(false)}
        />
      </Flex.Item>
    </Flex>
  )

  const renderStudentInfo = () => (
    <Text>
      {student ? (
        <>
          <IconUserLine size="x-small" /> {student.sortable_name} ({missingAttendanceCount} Alerts)
        </>
      ) : (
        `All Students (${missingAttendanceCount} Alerts)`
      )}
    </Text>
  )

  const tableForSingleStudent = () => (
    <Table caption={false} hover={true}>
      <Table.Head>
        <Table.Row>
          <Table.ColHeader id="date">Date</Table.ColHeader>
        </Table.Row>
      </Table.Head>
      <Table.Body>
        {missingAttendances.map((attendance, index) => (
          <Table.Row key={index}>
            <Table.Cell
              themeOverride={
                isCourseLocked(attendance.course_end_date) && {
                  background: '#F4F5F4',
                }
              }
            >
              <Link onClick={() => handleClick(attendance.attendance_date, attendance.canvas_id)}>
                {formatDate(attendance.attendance_date)}{' '}
                {isCourseLocked(attendance.course_end_date) && (
                  <Pill renderIcon={IconLockLine}>Locked</Pill>
                )}
              </Link>
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
    </Table>
  )

  const tableForAllStudents = () => (
    <Table caption={false} hover={true}>
      <Table.Head>
        <Table.Row>
          <Table.ColHeader id="date">Date</Table.ColHeader>
          <Table.ColHeader id="student">Student</Table.ColHeader>
        </Table.Row>
      </Table.Head>
      <Table.Body>
        {missingAttendances.map((attendance, index) => (
          <Table.Row key={index}>
            <Table.Cell
              themeOverride={
                isCourseLocked(attendance.course_end_date) && {
                  background: '#F4F5F4',
                }
              }
            >
              <Link onClick={() => handleClick(attendance.attendance_date, attendance.canvas_id)}>
                {formatDate(attendance.attendance_date)}{' '}
                {isCourseLocked(attendance.course_end_date) && (
                  <Pill>
                    <IconLockLine /> Locked
                  </Pill>
                )}
              </Link>
            </Table.Cell>
            <Table.Cell
              themeOverride={
                isCourseLocked(attendance.course_end_date) && {
                  background: '#F4F5F4',
                }
              }
            >
              <>
                <IconUserLine size="x-small" /> {attendance.sortable_name}
              </>
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
    </Table>
  )

  return (
    <div style={{ padding: '0 0 16rem 0', margin: '0 auto' }}>
      <Tray
        label="Missing Attendance Alerts"
        shouldCloseOnDocumentClick
        open={open}
        onOpen={fetchMissingAttendance}
        onDismiss={hideTray}
        onClose={hideTray}
        size="regular"
        placement="end"
      >
        <View as="div" padding="medium">
          {!loading && (
            <>
              <Flex direction="column">
                <Flex.Item>{renderHeader()}</Flex.Item>
                <Flex.Item padding="medium none">{renderStudentInfo()}</Flex.Item>
                <Flex.Item>{student ? tableForSingleStudent() : tableForAllStudents()}</Flex.Item>
              </Flex>
            </>
          )}
        </View>
      </Tray>
    </div>
  )
}

export default MissingAttendanceTray
