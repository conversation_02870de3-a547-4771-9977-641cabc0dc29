import React, { useRef, useState, useEffect } from 'react'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { View } from '@instructure/ui-view'
import { CloseButton, Button } from '@instructure/ui-buttons'
import { Tray } from '@instructure/ui-tray'
import { Text } from '@instructure/ui-text'
import { IconDownloadLine, IconResetSolid } from '@instructure/ui-icons'
import { Mask, Overlay } from '@instructure/ui-overlays'

import moment from 'moment'

import * as API from '../../utils/api'
import { Spinner } from '@instructure/ui-spinner'

const ExportReportTray = ({ open, setOpen }) => {
  const [showOverlay, setShowOverlay] = useState(false)
  const [recentReport, setRecentReport] = useState(null)
  const [reportState, setReportState] = useState({
    started: false,
    status: '',
    pollHandler: null,
  })

  const maskRef = useRef(null)
  const handleMaskRef = (el) => {
    maskRef.current = el
  }

  const generateCsvReport = async () => {
    setShowOverlay(true)
    await API.createReport()
      .then((response) => response.data)
      .then((data) => {
        let handler = null
        handler = setInterval(() => startPolling(data.id), 4000)
        setReportState((prevState) => ({
          ...prevState,
          started: true,
          status: data.status,
          pollHandler: handler,
        }))
      })
      .catch((error) => {
        console.error('Error generating CSV report:', error)
      })
  }

  useEffect(() => {
    if (reportState.pollHandler && !reportState.started) {
      clearInterval(reportState.pollHandler)
    }
  }, [reportState.started])

  const startPolling = async (reportId) => {
    let response = null
    try {
      response = await API.getReport(reportId)
      switch (response.data.status) {
        case 'complete':
          setReportState((prevState) => ({
            ...prevState,
            started: false,
            status: response.data.status,
          }))
          setRecentReport(response.data)
          setShowOverlay(false)
          break
        case 'complete_with_errors':
        case 'failed':
          setReportState((prevState) => ({
            ...prevState,
            started: false,
            status: 'error',
          }))
          setRecentReport(null)
          setShowOverlay(false)
          break
        default:
          break
      }
    } catch (err) {
      setReportState((prevState) => ({
        ...prevState,
        started: false,
        status: 'error',
      }))
      setShowOverlay(false)
    }
  }

  const hideTray = () => {
    setRecentReport(null)
    setShowOverlay(false)
    setOpen(false)
  }

  const fetchRecentReport = async () => {
    await API.getLatestCompletedReport()
      .then((response) => response.data)
      .then((data) => {
        if (Object.keys(data).length != 0) setRecentReport(data)
      })
  }

  const downloadFile = () => {
    recentReport && recentReport.download_url && window.open(recentReport.download_url, '_blank')
  }

  const renderHeader = () => (
    <Flex>
      <Flex.Item shouldGrow shouldShrink>
        <Heading level="h3">Export Report</Heading>
      </Flex.Item>
      <Flex.Item>
        <CloseButton placement="end" offset="small" screenReaderLabel="Close" onClick={hideTray} />
      </Flex.Item>
    </Flex>
  )

  return (
    <>
      <Tray
        label="Missing Attendance Alerts"
        open={open}
        onOpen={fetchRecentReport}
        onDismiss={hideTray}
        onClose={hideTray}
        size="regular"
        placement="end"
      >
        <View as="div" padding="medium">
          <Flex direction="column">
            <Flex.Item>{renderHeader()}</Flex.Item>
            <Flex.Item padding="medium none">
              <Text weight="normal" transform="uppercase">
                MISSING ATTENDANCE DATA REPORT
              </Text>
            </Flex.Item>
            <Flex.Item>
              <Text weight="bold">Generate Report</Text>
            </Flex.Item>
          </Flex>
          <Flex justifyItems="end" margin="medium">
            <Flex.Item>
              <Button color="primary" onClick={generateCsvReport} renderIcon={IconResetSolid}>
                Generate New File
              </Button>
            </Flex.Item>
          </Flex>
          {recentReport && (
            <View
              as="div"
              display="inline-block"
              borderColorPrimary="true"
              borderWidth="small"
              width="100%"
              padding="small"
            >
              <Flex direction="column" alignItems="start">
                <Flex.Item>
                  <Text weight="bold">File Generated</Text>
                </Flex.Item>
                <Flex.Item padding="small 0 small 0">
                  <Text size="x-small">{recentReport.file_name}</Text>
                </Flex.Item>
              </Flex>
              <Flex justifyItems="end" margin="small">
                <Button onClick={downloadFile} renderIcon={IconDownloadLine}>
                  Download CSV
                </Button>
              </Flex>
            </View>
          )}
        </View>

        <Overlay
          open={showOverlay}
          transition="fade"
          label="Overlay"
          shouldReturnFocus
          shouldContainFocus
          shouldCloseOnEscape={false}
          shouldCloseOnDocumentClick={false}
          defaultFocusElement={() => maskRef.current}
        >
          <Mask elementRef={handleMaskRef} fullscreen={true}>
            <Flex direction="column" alignItems="center" gap="medium">
              <Flex.Item>
                <Spinner
                  renderTitle="Loading"
                  size="small"
                  themeOverride={{ smallBorderWidth: '0.2rem' }}
                />
              </Flex.Item>

              <Flex.Item>Generating New Report...</Flex.Item>
            </Flex>
          </Mask>
        </Overlay>
      </Tray>
    </>
  )
}

export default ExportReportTray
