import React, { useState, useEffect } from 'react'
import { View, Table, Text, Flex, Spinner } from '@instructure/ui'
import { TextInput } from '@instructure/ui-text-input'
import { Pagination } from '@instructure/ui-pagination'

import * as API from './../../utils/api'

const COURSES_PER_PAGE = 25

const DefaultDailyAttendanceTableV2 = ({ pairs, setPairs, setInitialPairs }) => {
  const [loading, setLoading] = useState(true)
  const [editingLabel, setEditingLabel] = useState(null)

  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCourses, setTotalCourses] = useState(0)

  const fetchSubjectGradePairs = async () => {
    setLoading(true)

    await API.getSubjectGradePairAttendance({
      per_page: COURSES_PER_PAGE,
      page: currentPage,
    })
      .then((response) => response.data)
      .then((data) => {
        setInitialPairs(data.courses)
        setPairs(data.courses)
        setTotalCourses(data.pagination.total_items)
        setTotalPages(data.pagination.total_pages)
        setLoading(false)
      })
      .catch((error) => {
        console.error('Error fetching subject grade pairs:', error)
      })
  }

  useEffect(() => {
    const fetchData = async () => {
      await fetchSubjectGradePairs()
    }

    fetchData()
  }, [currentPage])

  if (loading) {
    return (
      <Flex alignItems="center" justifyItems="center" padding="xx-large">
        <Spinner renderTitle="Loading data..." />
      </Flex>
    )
  }

  const renderTableHeaders = () => (
    <Table.Row>
      <Table.ColHeader id="subject">Subject and Grade Level</Table.ColHeader>
      <Table.ColHeader id="daily-time-to-complete" textAlign="center">
        Default Daily Time to Complete
      </Table.ColHeader>
    </Table.Row>
  )

  const renderTableBody = () =>
    pairs.map((pair, index) => (
      <Table.Row key={pair.label}>
        <Table.Cell>{pair.subject} | Grade Level: {pair.grade_level}</Table.Cell>
        <Table.Cell textAlign="center" themeOverride={{ padding: '0.6rem 0 0 0' }}>
          <Flex justifyItems="space-around">
            <Flex.Item padding="none none small" width="9rem">
              {renderTextInput(pair)}
            </Flex.Item>
          </Flex>
        </Table.Cell>
      </Table.Row>
    ))

  const renderTextInput = (pair) => (
    <TextInput
      renderLabel={false}
      value={
        editingLabel === pair.label
          ? `${pair.default_time_to_complete}`
          : pair.default_time_to_complete != null
            ? formatTime(pair.default_time_to_complete)
            : ''
      }
      maxLength={4}
      textAlign="center"
      onFocus={(event) => {
        setEditingLabel(pair.label)

        requestAnimationFrame(() => {
          event.target.select()
        })
      }}
      themeOverride={
        pair.default_time_to_complete > 1440
          ? {
              borderColor: '#C61F23',
            }
          : {}
      }
      onBlur={() => setEditingLabel(null)}
      onChange={(_e, value) => handleTimeChange(pair.label, value)}
    />
  )

  const formatTime = (minutes) => {
    const hrs = Math.floor(minutes / 60)
    const mins = minutes % 60
    return hrs > 0 ? `${hrs} hrs ${mins} min` : `${mins} min`
  }

  const handleTimeChange = (label, newTimeStr) => {
    const newTime = (parseInt(newTimeStr, 10) || 0).toString()

    const updatedPairs = pairs.map((pair) =>
      pair.label === label ? { ...pair, default_time_to_complete: newTime } : pair,
    )

    setPairs(updatedPairs)
  }

  return (
    <View margin="small">
      <Flex direction="column" gap="medium">
        <Flex.Item>
          <Text size="medium">Set Default Daily Attendance Values</Text>
        </Flex.Item>
        <Flex.Item>
          <Text size="small">
            {totalCourses} Subjects and Grade Levels {totalPages > 1 && `on ${totalPages} Pages`}
          </Text>
        </Flex.Item>
        <Flex.Item>
          <Table caption={false} layout="fixed" hover>
            <Table.Head>{renderTableHeaders()}</Table.Head>
            <Table.Body>{renderTableBody()}</Table.Body>
          </Table>

          {totalPages > 1 && (
            <Pagination
              as="nav"
              margin="large small small"
              variant="compact"
              labelNext="Next Page"
              labelPrev="Previous Page"
              labelFirst="First Page"
              labelLast="Last Page"
              withFirstAndLastButton
              currentPage={currentPage}
              onPageChange={(nextPage) => setCurrentPage(nextPage)}
              totalPageNumber={totalPages}
              siblingCount={3}
              boundaryCount={2}
            />
          )}
        </Flex.Item>
      </Flex>
    </View>
  )
}

export default DefaultDailyAttendanceTableV2
