import React, { useEffect, useState } from 'react'
import {
  View,
  Flex,
  Text,
  Breadcrumb,
  <PERSON><PERSON>,
  Link,
  CondensedButton,
  IconButton,
  Popover,
  Button,
  Calendar,
  List,
  Pill,
  Alert,
} from '@instructure/ui'
import {
  IconUserLine,
  IconAddressBookLine,
  IconCalendarMonthLine,
  IconArrowOpenStartLine,
  IconArrowOpenEndSolid,
  IconArrowOpenStartSolid,
  IconArrowOpenEndLine,
  IconArrowOpenDownLine,
  IconSaveLine,
} from '@instructure/ui-icons'

import ContactInformation from '../shared/ContactInformation'
import MissingAttendanceTray from '../attendance/MissingAttendanceTray'
import CourseTimeTable from '../../components/attendance/CourseTimeTable'

import moment from 'moment'

import * as API from '../../utils/api'

const StudentAttendance = ({ studentId, selectedDate, onBack }) => {
  const [loading, setLoading] = useState(true)
  const [student, setStudent] = useState(null)

  const [missingAttendanceStudent, setMissingAttendanceStudent] = useState(null)
  const [isMissingAttendanceTrayOpen, setIsMissingAttendanceTrayOpen] = useState(false)

  const [isContactInformationModalOpen, setIsContactInformationModalOpen] = useState(false)

  const openContactInformationModal = () => setIsContactInformationModalOpen(true)
  const closeContactInformationModal = () => setIsContactInformationModalOpen(false)

  const [courses, setCourses] = useState([])
  const [initialCourses, setInitialCourses] = useState([])

  const [invalidAttendance, setInvalidAttendance] = useState(false)

  const [isPopoverOpen, setPopoverOpen] = useState(false)
  const [recordAttendance, setRecordAttendance] = useState(false)

  const [missingAttendanceCount, setMissingAttendanceCount] = useState(0)

  const [date, setDate] = useState(
    selectedDate ? moment(selectedDate, 'DD-MM-YYYY').toDate() : new Date(),
  )
  const [isDateLocked, setDateLocked] = useState(false)
  const [isDateWeekend, setDateWeekend] = useState(false)
  const [isDateFuture, setDateFuture] = useState(false)
  const [weekendLabel, setWeekendLabel] = useState('')
  const [hasLockedCourses, setHasLockedCourses] = useState(false)

  const [showDatePicker, setShowDatePicker] = useState(false)

  const setDateStatus = (date) => {
    const momentDate = moment(date)
    const formattedDate = momentDate.format('DD-MM-YYYY')
    const isWeekend = [6, 7].includes(momentDate.isoWeekday())
    setDateFuture(momentDate.isAfter(moment()))

    if (isWeekend) {
      setDateWeekend(true)
      setWeekendLabel('Weekend')
      return
    }

    const exceptionDate = window.ENV.exception_dates.find(
      (exception) => exception.exception_date === formattedDate,
    )

    if (exceptionDate) {
      setDateWeekend(true)
      const exceptionDateLabel = exceptionDate.exception_type
        .toLowerCase()
        .replace(/\b\w/g, (char) => char.toUpperCase())
      setWeekendLabel(exceptionDateLabel)
      return
    }

    setDateWeekend(false)
  }

  const fetchUserCourses = async () => {
    if (!student) return

    setCourses([])
    setHasLockedCourses(false)

    await API.getUserCourses(student.canvas_id, { date: moment(date).format('DD-MM-YYYY') })
      .then((response) => response.data)
      .then((data) => {
        setInitialCourses(structuredClone(data.courses))
        setCourses(data.courses)
      })
      .catch((error) => {
        console.error('Error fetching user courses:', error)
      })
  }

  useEffect(() => {
    if (!student) return

    fetchUserCourses()
  }, [student])

  useEffect(() => {
    if (!date) return

    const fetchData = async () => {
      setDateStatus(date)
      await fetchUserCourses()
    }

    fetchData()
  }, [date])

  const getStudent = async (student_id) => {
    try {
      const response = await API.getUser(student_id || studentId)
      setStudent(response.data)
    } catch (error) {
      console.error('Error fetching missing attendance:', error)
    }
  }

  const handleShowMissingAttendanceAlerts = async (student) => {
    setMissingAttendanceStudent(student)
    setIsMissingAttendanceTrayOpen(true)
  }

  const fetchMissingAttendanceCount = async (params = {}) => {
    await API.getMissingAttendance(params)
      .then((response) => {
        setMissingAttendanceCount(response.data.missing_attendance_count)
      })
      .catch((error) => {
        console.error('Error fetching missing attendance count:', error)
      })
  }

  const reloadData = async () => {
    setLoading(true)

    await fetchMissingAttendanceCount()
    await getStudent()
    await fetchUserCourses()

    setLoading(false)
  }

  useEffect(() => {
    const fetchData = async () => {
      await reloadData()
    }

    fetchData()
  }, [])

  const handleTrayItemClick = async (date, student_id) => {
    setDate(moment(date, 'DD-MM-YYYY').toDate())
    await getStudent(student_id)
    setIsMissingAttendanceTrayOpen(false)
  }

  const dateFormatter = (date) => {
    if (!(date instanceof Date) || isNaN(date)) return ''

    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }
    return `${new Intl.DateTimeFormat('en-US', options).format(date)}`
  }

  const handleCoursesUpdate = (updatedCourses) => {
    setCourses(updatedCourses)
  }

  const handleAutoEnter = (type) => {
    if (isDateFuture) {
      setPopoverOpen(false)
      return
    }

    const updatedCourses = courses.map((course) => {
      const updatedCourse = { ...course }
      if (updatedCourse.disable_attendance_recording || updatedCourse.locked) {
        setHasLockedCourses(true)
        return updatedCourse
      }

      if (type === 'zero') {
        updatedCourse.time_in_minutes = '0'
      } else if (type === 'default' && course.default_time_to_complete) {
        updatedCourse.time_in_minutes = course.default_time_to_complete.toString()
      }
      return updatedCourse
    })

    setPopoverOpen(false)
    setCourses(updatedCourses)
  }

  useEffect(() => {
    if (!initialCourses.length) return

    const invalidAttendance =
      courses
        .filter((course) => course.time_in_minutes != null)
        .reduce((sum, course) => sum + parseInt(course.time_in_minutes), 0) > 1440
    setInvalidAttendance(invalidAttendance)
    if (invalidAttendance) {
      setRecordAttendance(false)
      return
    }

    if (JSON.stringify(initialCourses) == JSON.stringify(courses)) {
      setRecordAttendance(false)
      return
    }

    setRecordAttendance(true)
  }, [courses])

  const handleSave = async () => {
    setLoading(true)

    const params = {
      date: moment(date).format('DD-MM-YYYY'),
      courses: courses
        .filter((course) => course.time_in_minutes != null)
        .map(({ canvas_id, name, time_in_minutes, organization_name, enrollment_canvas_user_id }) => ({
          canvas_id,
          name,
          time_in_minutes,
          organization_name,
          enrollment_canvas_user_id
        })),
    }

    await API.recordAttendance(student.canvas_id, params)
      .then((response) => response.data)
      .then(async () => {
        await reloadData()
        setLoading(false)
      })
      .catch(function (error) {
        console.error('Error recording attendance:', error)
      })
  }

  const handleRestore = () => {
    setCourses(initialCourses)
  }

  const renderLockedCoursesAlert = () => {
    return (
      <Flex margin="small">
        <Flex.Item shouldGrow>
          <Alert variant="warning">
            <Text weight="bold">Locked:</Text> One or more courses is locked and will not have time
            applied!
          </Alert>
        </Flex.Item>
      </Flex>
    )
  }

  if (loading) {
    return (
      <Flex alignItems="center" justifyItems="center" padding="xx-large">
        <Spinner renderTitle="Loading students..." />
      </Flex>
    )
  }

  return (
    <>
      {invalidAttendance && (
        <div style={{ padding: '0 10rem' }}>
          <Alert variant="warning">
            <Text weight="bold">Invalid:</Text> The student's daily total attendance value cannot
            exceed 1,440 minutes (24 hrs).
          </Alert>
        </div>
      )}

      <View padding="medium">
        <Flex justifyItems="space-between">
          <Flex.Item shouldGrow padding="xxx-small 0">
            <Breadcrumb label="">
              <Breadcrumb.Link onClick={onBack}>All Students</Breadcrumb.Link>
              <Breadcrumb.Link>Student:</Breadcrumb.Link>
            </Breadcrumb>
          </Flex.Item>
          <Flex.Item align="end" padding="xxx-small 0">
            <Text weight="bold">
              Missing Attendance (All Students){' '}
              <Link
                themeOverride={{
                  focusOutlineWidth: 0,
                }}
                onClick={() => handleShowMissingAttendanceAlerts()}
              >
                {missingAttendanceCount} Alerts
              </Link>
            </Text>
          </Flex.Item>
        </Flex>

        <Flex justifyItems="space-between" padding="x-small 0">
          <Flex.Item shouldGrow>
            <Flex justifyItems="start" alignItems="start" gap="x-small">
              <Flex.Item padding="x-small none none none">
                <IconUserLine size="x-small" />
              </Flex.Item>
              <Flex.Item shouldGrow>
                <Flex direction="column">
                  <Flex.Item>
                    <Flex justifyItems="space-between">
                      <Flex.Item shouldGrow padding="xx-small none none none">
                        <Text weight="bold" size="large">
                          {student.sortable_name}
                        </Text>
                      </Flex.Item>
                      <Flex.Item align="start" padding="xxx-small 0">
                        <Text weight="bold">
                          Missing Attendance{' '}
                          <Link
                            href="#"
                            themeOverride={{
                              focusOutlineWidth: 0,
                            }}
                            onClick={() => handleShowMissingAttendanceAlerts(student)}
                          >
                            {student.missing_attendance_count || 0} Alerts
                          </Link>
                        </Text>
                      </Flex.Item>
                    </Flex>
                  </Flex.Item>
                  <Flex.Item>
                    <Flex>
                      <Flex.Item>
                        <Text>
                          ID: {student.sis_id} | Grade Level: {student.grade_level} | Learning
                          Coach:{' '}
                          {student.learning_coaches
                            ?.map((coach) => coach.sortable_name)
                            ?.join('; ')}{' '}
                        </Text>
                      </Flex.Item>
                      <Flex.Item align="center">
                        <CondensedButton
                          size="small"
                          margin="x-small"
                          onClick={openContactInformationModal}
                          renderIcon={IconAddressBookLine}
                        />
                      </Flex.Item>
                    </Flex>
                  </Flex.Item>
                </Flex>
              </Flex.Item>
            </Flex>
          </Flex.Item>
        </Flex>

        <Flex margin="small">
          <Flex.Item shouldGrow>
            <IconButton
              screenReaderLabel="Previous Date"
              margin="none small none none"
              onClick={() => {
                const newDate = date.setDate(date.getDate() - 1)
                setDate(new Date(newDate))
              }}
            >
              <IconArrowOpenStartLine />
            </IconButton>
            <Popover
              renderTrigger={
                <View as="span" width="22rem" display="inline-flex">
                  <Button display="block" color="primary-inverse">
                    <Flex justifyItems="space-between">
                      <Flex.Item>{dateFormatter(date)}</Flex.Item>
                      <Flex.Item>
                        {isDateWeekend ? (
                          <Pill>{weekendLabel}</Pill>
                        ) : isDateLocked ? (
                          <Pill>Locked</Pill>
                        ) : (
                          ''
                        )}
                      </Flex.Item>
                      <Flex.Item>
                        <IconCalendarMonthLine />
                      </Flex.Item>
                    </Flex>
                  </Button>
                </View>
              }
              isShowingContent={showDatePicker}
              onShowContent={() => setShowDatePicker(true)}
              onHideContent={() => setShowDatePicker(false)}
              on="click"
              shouldContainFocus
              shouldReturnFocus
              shouldCloseOnDocumentClick
            >
              <Calendar
                onDateSelected={(date) => {
                  setDate(new Date(date))
                  setShowDatePicker(false)
                }}
                selectedDate={date.toISOString()}
                role="listbox"
                visibleMonth={date.toISOString()}
                renderNextMonthButton={
                  <IconButton
                    size="small"
                    withBackground={false}
                    withBorder={false}
                    renderIcon={<IconArrowOpenEndSolid color="primary" />}
                    screenReaderLabel="Next month"
                  />
                }
                renderPrevMonthButton={
                  <IconButton
                    size="small"
                    withBackground={false}
                    withBorder={false}
                    renderIcon={<IconArrowOpenStartSolid color="primary" />}
                    screenReaderLabel="Previous month"
                  />
                }
              />
            </Popover>
            <IconButton
              screenReaderLabel="Next Date"
              margin="none none none small"
              onClick={() => {
                const newDate = date.setDate(date.getDate() + 1)
                setDate(new Date(newDate))
              }}
            >
              <IconArrowOpenEndLine />
            </IconButton>
          </Flex.Item>
          <Flex.Item>
            <Popover
              isShowingContent={isPopoverOpen}
              on="click"
              shouldAlignArrow
              offsetX="10rem"
              onShowContent={() => {
                setPopoverOpen(true)
              }}
              onHideContent={() => {
                setPopoverOpen(false)
              }}
              renderTrigger={
                <Button onClick={() => setPopoverOpen((prev) => !prev)} disabled={isDateFuture}>
                  Auto Enter <IconArrowOpenDownLine />
                </Button>
              }
            >
              <View>
                <View padding="xx-small 0 0 xx-small">
                  <Text as="span" size="small" weight="bold">
                    Apply to Each Course:
                  </Text>
                  <hr />
                </View>
                <List
                  isUnstyled
                  themeOverride={{
                    listPadding: 0,
                  }}
                >
                  <List.Item>
                    <Link
                      href="#"
                      display="block"
                      style={{
                        padding: '8px 0',
                      }}
                      themeOverride={{
                        focusOutlineWidth: 0,
                        textDecorationWithinText: 'none',
                        color: 'black',
                        hoverColor: 'white',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#0374B5'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent'
                      }}
                      onClick={() => handleAutoEnter('default')}
                    >
                      &nbsp;Default Time Values
                    </Link>
                  </List.Item>
                  <List.Item>
                    <Link
                      href="#"
                      display="block"
                      style={{
                        padding: '8px 0',
                      }}
                      themeOverride={{
                        focusOutlineWidth: 0,
                        textDecorationWithinText: 'none',
                        color: 'black',
                        hoverColor: 'white',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = '#0374B5'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent'
                      }}
                      onClick={() => handleAutoEnter('zero')}
                    >
                      &nbsp;0 Minutes
                    </Link>
                  </List.Item>
                </List>
              </View>
            </Popover>
          </Flex.Item>
        </Flex>

        {hasLockedCourses && renderLockedCoursesAlert()}

        {isDateFuture && (
          <Flex margin="small">
            <Flex.Item shouldGrow>
              <Alert variant="info" renderCloseButtonLabel="Close">
                <Text weight="bold">Locked:</Text> Attendance for future date can not be added.
              </Alert>
            </Flex.Item>
          </Flex>
        )}

        <View padding="none x-large">
          <CourseTimeTable
            courses={courses}
            onCoursesUpdate={handleCoursesUpdate}
            disableInput={isDateFuture}
          />
        </View>

        <Flex justifyItems="end">
          <Flex.Item>
            <Button
              variant="secondary"
              onClick={handleRestore}
              disabled={JSON.stringify(initialCourses) == JSON.stringify(courses)}
            >
              Restore
            </Button>
          </Flex.Item>

          <Flex.Item>
            <Button
              color="primary"
              margin="small"
              disabled={!recordAttendance}
              onClick={handleSave}
            >
              <IconSaveLine /> Save
            </Button>
          </Flex.Item>
        </Flex>

        <ContactInformation
          isOpen={isContactInformationModalOpen}
          onClose={closeContactInformationModal}
          learningCoaches={student.learning_coaches || []}
        />

        <MissingAttendanceTray
          setOpen={setIsMissingAttendanceTrayOpen}
          open={isMissingAttendanceTrayOpen}
          student={missingAttendanceStudent}
          handleClick={handleTrayItemClick}
        />
      </View>
    </>
  )
}

export default StudentAttendance
