import axios from './axios'

export const getUsers = (params = {}) => {
  return axios.get('users', { params: params })
}

export const searchUsers = (params = {}) => {
  return axios.get(`users/search`, { params: params })
}

export const getObservedUsers = () => {
  return axios.get('/users/observed_users')
}

export const getMissingAttendance = (params = {}) => {
  return axios.get(`attendance/missing`, { params: params })
}

export const getUser = (id) => {
  return axios.get(`users/${id}`)
}

export const getUserCourses = (id, params = {}) => {
  return axios.get(`users/${id}/courses`, { params: params })
}

export const getBlueprintCourses = (params = {}) => {
  return axios.get('courses/blueprint', { params })
}

export const bulkUpdateCourses = (params = {}) => {
  return axios.post('courses/bulk_update', params)
}

export const bulkUpdatePairs = (params = {}) => {
  return axios.post('courses/bulk_update_pairs', params)
}

export const recordAttendance = (user_id, params = {}) => {
  return axios.post(`users/${user_id}/attendance/record`, params)
}

export const getLatestCompletedReport = (params = {}) => {
  return axios.get('/reports/latest_completed_report', params)
}

export const createReport = (params = {}) => {
  return axios.post('/reports', params)
}

export const getReport = (id) => {
  return axios.get(`/reports/${id}`)
}

export const getAccount = (id) => {
  return axios.get(`/accounts/${id}`)
}

export const updateAccount = (id, params = {}) => {
  return axios.put(`/accounts/${id}`, params)
}

export const latestImport = () => {
  return axios.get('imports/latest_import')
}

export const getImport = (id) => {
  return axios.get(`/imports/${id}`)
}

export const fileImport = (params = {}) => {
  return axios.post(`/imports`, params, {
    headers: {
      'content-type': 'multipart/form-data',
    },
  })
}

export const getSubjectGradePairAttendance = (params = {}) => {
  return axios.get('courses/subject_grade_pairs', { params })
}
