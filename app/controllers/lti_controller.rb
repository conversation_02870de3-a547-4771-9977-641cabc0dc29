# frozen_string_literal: true

class LtiController < ApplicationController
  before_action :set_js_env

  def account_navigation
    authorize! :launch_from, :account

    component = if current_ability.user_is_stride_admin?
      'StrideAdminDashboard'
    elsif current_ability.user_is_school_admin?
      'SchoolAdminDashboard'
    else
      'UnAuthorized'
    end

    render component:, prerender: false
  end

  def course_navigation
    authorize! :launch_from, :course

    component = if current_ability.user_is_stride_admin?
                  'StrideAdminDashboard'
    elsif current_ability.user_is_school_admin?
                  'SchoolAdminDashboard'
    elsif current_ability.user_is_teacher?
                  'TeacherDashboard'
    elsif current_ability.user_is_observer?
                  'LearningCoachDashboard'
    else
                  'UnAuthorized'
    end

    render component:, prerender: false
  end

  def user_navigation
    authorize! :launch_from, :user

    render component: 'LearningCoachDashboard', prerender: false
  end

  private

  def set_js_env
    js_env({
      user_is_stride_admin: current_ability.user_is_stride_admin?,
      user_is_school_admin: current_ability.user_is_school_admin?,
      user_is_teacher: current_ability.user_is_teacher?,
      user_is_observer: current_ability.user_is_observer?,
      exception_dates: exception_dates,
      launch_point: action_name
    })
  end

  def exception_dates
    ExceptionDate
      .select(
        "TO_CHAR(date, 'DD-MM-YYYY') as exception_date",
        'exception_type'
      )
    .as_json(only: %w[exception_date exception_type])
  end
end
