class Api::V1::Accounts<PERSON>ontroller < ApplicationController
  before_action :set_account

  def update
    authorize! :update, :account

    @account.update!(account_params)
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error "Error: #{e.message}"
    Rails.logger.error e.backtrace
    render json: { error: 'Failed to update account', details: e.message }, status: :unprocessable_entity
  end

  private

  def set_account
    @account = Account.find_by_canvas_id!(params[:id])
  end

  def account_params
    params.require(:account).permit(:enabled, :lockout_date, :lockout_date_setting, :lockout_days, :school_year,
                                              :default_time_to_complete)
  end
end
