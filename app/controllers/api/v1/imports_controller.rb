# frozen_string_literal: true

class Api::V1::ImportsController < ApplicationController
  skip_before_action :verify_authenticity_token

  def create
    @import = Import.new(import_params)
    validate_params
    @import.file.attach(import_params[:file])
    @import.uploaded_by_user_id = current_user.canvas_id
    @import.save!
    @import.process_file
    render :show
  rescue StandardError => e
    render json: {
      status: :unprocessable_entity,
      message: e.message
    }
  end

  def latest_import
    @import = Import.where.not(status: %w[queued processing]).last
    render :show
  end

  def show
    @import = Import.find(params[:id])
  end

  private

  def import_params
    params.require(:import).permit(:file)
  end

  def validate_params
    raise 'Not a valid file, must be .csv file' unless valid_file?
  end

  def valid_file?
    params[:import][:file].present? && params[:import][:file].content_type == 'text/csv'
  end
end
