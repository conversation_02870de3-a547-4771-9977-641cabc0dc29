# frozen_string_literal: true

class Api::V1::UsersController < ApplicationController
  def index
    users = fetch_users
              .includes(:active_learning_coaches)
              .order("#{sort_key} #{sort_order}")

    @paginated_data = paginated(users, pagination_params)

    @missing_attendance_data = Attendance.where(
      canvas_user_id: @paginated_data.result.map(&:canvas_id),
      canvas_course_id: active_canvas_course_ids,
      time_in_minutes: nil
    ).select(
      'canvas_user_id, COUNT(DISTINCT attendance_date) AS attendance_count'
    ).group(:canvas_user_id).to_h { |r| [ r.canvas_user_id, r.attendance_count ] }
  end

  def search
    users = fetch_users
    render json: {
      data: users.where(search_query, search: "%#{params[:search]}%")
                 .as_json(only: %i[canvas_id sis_id sortable_name])
    }, status: :ok
  end

  def show
    @user = User.find_by_canvas_id!(params[:id])
    @missing_attendance_count = @user.attendances.where(
      time_in_minutes: nil,
      canvas_course_id: active_canvas_course_ids
    ).select(:attendance_date).distinct.count
  end

  def observed_users
    @users = QueryBuilder::ObservedUsers.new(observer: current_user).records

    attendance_records = QueryBuilder::MissingAttendance.new(observer: current_user).observed_user_records

    # grouping and preserving both sharded and normalized user_id with their combined counts
    attendance_grouped_data = attendance_records.group_by { |r| r.canvas_id % PandaPal::Organization::SHARD_OFFSET }
                               .transform_values(&:count)
    @missing_attendance_data = attendance_records.map(&:canvas_id).uniq.index_with do |canvas_user_id|
      normalized_id = canvas_user_id % PandaPal::Organization::SHARD_OFFSET
      attendance_grouped_data[normalized_id]
    end
  end

  private

  def fetch_users
    if current_ability.user_is_stride_admin?
      User.active_students
    elsif current_ability.user_is_school_admin?
      User.active_students_for_account(current_account.canvas_id)
    elsif current_ability.user_is_teacher?
      User.active_students_in_course_ids(active_canvas_course_ids)
    else
      User.none
    end
  end

  def search_query
    'first_name ILIKE :search OR last_name ILIKE :search OR sortable_name ILIKE :search'
  end

  def sort_key
    return params[:sort_key] if %w[users.sortable_name users.grade_level].include?(params[:sort_key])

    'users.sortable_name'
  end

  def sort_order
    params[:sort_order].to_s.upcase.in?(%w[ASC DESC]) ? params[:sort_order] : 'ASC'
  end

  def active_canvas_course_ids
    if current_ability.user_is_stride_admin?
      Course.active.pluck(:canvas_id)
    elsif current_ability.user_is_school_admin?
      current_account.courses.active.pluck(:canvas_id)
    elsif current_ability.user_is_teacher?
      current_user.active_teaching_courses.pluck(:canvas_id)
    else
      []
    end
  end
end
