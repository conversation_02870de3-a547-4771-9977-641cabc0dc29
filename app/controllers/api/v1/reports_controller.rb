class Api::V1::ReportsController < ApplicationController
  def create
    authorize! :create, :reports
    @report = Report.new
    @report.report_arguments[:account_id] = current_account.canvas_id unless current_ability.user_is_stride_admin?
    @report.created_by_user_id = current_user.canvas_id
    @report.status = :queued
    @report.save!
    @report.process_report

    render :show
  rescue StandardError => e
    Rails.logger.error "Error: #{e.message}"
    Rails.logger.error e.backtrace
    render json: { error: e.message }, status: :unprocessable_entity
  end

  def show
    authorize! :show, :reports
    @report = Report.find(params[:id])
  end

  def latest_completed_report
    authorize! :show, :reports
    @report = Report.complete.by_user_id(current_user.canvas_id).first

    render :show
  end
end
