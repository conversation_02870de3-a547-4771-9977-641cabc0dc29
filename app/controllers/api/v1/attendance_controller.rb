# frozen_string_literal: true

class Api::V1::AttendanceController < ApplicationController
  class StudentNotFound < StandardError; end
  class CourseNotFound < StandardError; end
  class NoStudentEnrollmentInCourse < StandardError; end
  class InvalidDate < StandardError; end
  class InvalidMinutes < StandardError; end
  class MinutesExceedDailyMaximum < StandardError; end

  before_action :check_organization, only: %i[attendance]
  before_action :authenticate_with_token, only: %i[attendance]

  skip_before_action :forbid_access_if_lacking_session, only: %i[attendance]

  def record
    raise 'Not authorized' unless  current_ability.record_attendance?

    is_stride_admin = current_ability.user_is_stride_admin?

    organization = PandaPal::Organization.find_by_name(params[:organization_name]) || current_organization

    organization.switch_tenant do
      ActiveRecord::Base.transaction do
        grouped_organization_data = params[:courses].group_by { |p| p[:organization_name] }

        grouped_organization_data.each do |org_name, org_params|
          PandaPal::Organization.find_by(name: org_name).switch_tenant do
            # Considering the attendance record is just for a single canvas user in that grouped organization data
            canvas_user_id = org_params.first[:enrollment_canvas_user_id]

            student = User.find_by_canvas_id(canvas_user_id)

            courses = student.courses
              .active_student_enrollments
              .where(canvas_id: org_params.pluck(:canvas_id))

            # Stride Administrators can record attendance for all courses beyond the start and end dates
            courses = courses.for_date(params[:date].to_date) unless is_stride_admin

            courses_canvas_ids = courses.pluck(:canvas_id)

            attendance_data = org_params.select { |c| c[:canvas_id].to_i.in?(courses_canvas_ids) }

            attendance_data.each do |attendance_info|
              attendance = Attendance.find_or_initialize_by(
                user: student,
                canvas_course_id: attendance_info[:canvas_id],
                attendance_date: params[:date].to_date
              )
              attendance.actioning_user = current_user
              attendance.time_in_minutes = attendance_info[:time_in_minutes]
              attendance.save!
            end
          end
        end
      end
    end

    render json: { message: 'Attendance recorded successfully' }, status: :ok
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error "Error: #{e.message}"
    Rails.logger.error e.backtrace
    render_error('Failed to record attendance', e.message, :unprocessable_entity)
  rescue StandardError => e
    handle_standard_error(e)
  end

  def missing
    if current_ability.user_is_observer?
      attendance_query_builder = QueryBuilder::MissingAttendance.new(observer: current_user, user_id: params[:user_id])
      records = attendance_query_builder.observed_user_records
      missing_attendance_records = attendance_query_builder.normalized_sorted_records(records)

      render json: {
        missing_attendance_count: missing_attendance_records.size,
        data: missing_attendance_records
      }, status: :ok and return
    end

    user_canvas_ids = if params[:user_id] && current_ability.view_attendance?
                        User.where(canvas_id: params[:user_id]).pluck(:canvas_id)
    elsif current_ability.user_is_stride_admin?
                        User.active_students.pluck(:canvas_id)
    elsif current_ability.user_is_school_admin?
                        User.active_students_for_account(current_account.canvas_id).pluck(:canvas_id)
    elsif current_ability.user_is_teacher?
                        User.joins(:enrollments).where(enrollments: {
                                                         workflow_state: 'active',
                                                         base_role_type: 'StudentEnrollment',
                                                         canvas_course_id: current_user.courses.pluck(:canvas_id)
                                                       })
                            .pluck(:canvas_id)
    else
                        # TODO: Handle Non-Observer User Cases
                        User.none.pluck(:canvas_id)
    end

    if params[:only_count]
      missing_attendance_count = Attendance.where(
        canvas_user_id: user_canvas_ids,
        canvas_course_id: active_canvas_course_ids,
        time_in_minutes: nil
      ).select('DISTINCT attendance_date, canvas_user_id').size

      render json: { missing_attendance_count: missing_attendance_count }, status: :ok
    else
      missing_attendance_records = Attendance
        .joins(:user)
        .where(canvas_user_id: user_canvas_ids, time_in_minutes: nil, canvas_course_id: active_canvas_course_ids)
        .order('attendance_date ASC, users.sortable_name ASC')
        .select(
          'DISTINCT attendance_date, canvas_user_id AS canvas_id, users.sortable_name'
        )

      render json: {
        missing_attendance_count: missing_attendance_records.size,
        data: missing_attendance_records.as_json(
          only: %i[canvas_id attendance_date sortable_name]
        )
      }, status: :ok
    end
  end

  # External API
  # POST /organizations/:organization_id/api/v1/:student_id/attendance
  def attendance
    student = find_student(params[:student_id])
    course = find_course(params[:course_sis_id])
    validate_student_enrollment(course, student)
    minutes = validate_minutes(params[:minutes])
    date = validate_date(params[:date], course)

    attendance = Attendance.find_or_initialize_by(user: student, course: course, attendance_date: date)
    attendance.actioning_user = current_user
    attendance.time_in_minutes = minutes
    attendance.save!

    render json: { message: 'Attendance recorded successfully' }, status: :ok
  rescue ActiveRecord::RecordNotFound => e
    render_error("#{e.model} Not Found", "#{e.model} not found", :not_found)
  rescue StudentNotFound => e
    render_error('Student Not Found', e.message, :not_found)
  rescue CourseNotFound
    render_error('Course Not Found',
                 'No course was found with a SIS ID matching the course_sis_id provided in the request.', :not_found)
  rescue NoStudentEnrollmentInCourse
    render_error('No Student Enrollment in Course', 'Student is not enrolled in the provided course', :not_found)
  rescue InvalidDate
    render_error('Invalid Date', 'Invalid Date Provided', :bad_request)
  rescue InvalidMinutes
    render_error('Invalid Minutes', 'The minutes value provided is invalid', :bad_request)
  rescue MinutesExceedDailyMaximum
    render_error('Minutes Exceed Daily Maximum',
                 'The minutes value provided exceed, or if added would exceed, the maximum amount of daily minutes.',
                 :bad_request)
  rescue StandardError => e
    handle_standard_error(e)
  end

  private

  def find_student(student_id)
    User.find_by!('canvas_id = ? OR login_id = ? OR sis_id = ?', student_id.to_i,
                student_id.gsub('sislogin:', ''), student_id.gsub('sis:', ''))
  rescue ActiveRecord::RecordNotFound
    raise StudentNotFound, "Student with ID or SIS ID or LOGIN ID #{student_id} not found"
  end

  def find_course(course_sis_id)
    Course.find_by!(sis_id: course_sis_id)
  rescue ActiveRecord::RecordNotFound
    raise CourseNotFound, "Course with SIS ID #{course_sis_id} not found"
  end

  def validate_student_enrollment(course, student)
    unless course.enrollments.student.active.exists?(user: student)
      raise NoStudentEnrollmentInCourse, 'Student is not enrolled in the provided course'
    end
  end

  def validate_minutes(minutes_param)
    minutes = Integer(minutes_param)
    raise InvalidMinutes if minutes < 0
    raise MinutesExceedDailyMaximum if minutes > 1440
    minutes
  rescue ArgumentError, TypeError
    raise InvalidMinutes
  end

  def validate_date(date_param, course)
    date = Date.strptime(date_param, '%Y-%m-%d')
    raise InvalidDate if course.locked?(date) || !date.between?(course.resolved_start_date, course.resolved_end_date)
    date
  rescue Date::Error
    raise InvalidDate
  end

  def render_error(error, details, status)
    render json: { error: error, error_details: details }, status: status
  end

  def handle_standard_error(e)
    Rails.logger.error "Error: #{e.message}"
    Rails.logger.error e.backtrace
    render json: { error: 'An unexpected error occurred', details: e.message }, status: :internal_server_error
  end

  def active_canvas_course_ids
    if current_ability.user_is_stride_admin?
      Course.active.pluck(:canvas_id)
    elsif current_ability.user_is_school_admin?
      current_account.courses.active.pluck(:canvas_id)
    elsif current_ability.user_is_teacher?
      current_user.active_teaching_courses.pluck(:canvas_id)
    elsif current_ability.user_is_observer?
      Course.active.enabled_accounts.pluck(:canvas_id)
    else
      []
    end
  end
end
