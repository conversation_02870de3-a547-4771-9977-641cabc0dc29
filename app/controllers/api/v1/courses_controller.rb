# frozen_string_literal: true

class Api::V1::CoursesController < ApplicationController
  before_action :set_user, only: :index

  def index
    @is_stride_admin    = current_ability.user_is_stride_admin?
    @is_account_admin = current_ability.user_is_school_admin?
    @is_teacher       = current_ability.user_is_teacher?
    @is_observer      = current_ability.user_is_observer?

    teaching_course_ids = current_user.active_teaching_courses.pluck(:canvas_id) if @is_teacher
    account_id          = current_account.canvas_id if @is_account_admin

    @attendance_to_date_data = {}
    @attendance_for_date_data = {}

    if @is_observer
      query_builder = QueryBuilder::ObserverStudentCourses.new(student: @user, date: params[:date])
      @courses = query_builder.records
      @attendance_for_date_data = query_builder.attendance_for_date_data
      @attendance_to_date_data = query_builder.attendance_to_date_data
    else
      organization = current_organization
      @courses = @user.courses
                      .active_student_enrollments
                      .includes(:blueprint_course, :term, :account)
                      .order(:name)
                      .distinct
                      .select(
                        <<~SQL.squish
                          courses.*,
                          enrollments.canvas_user_id AS enrollment_canvas_user_id,
                          #{organization.id} AS organization_id,
                          '#{organization.name}' AS organization_name
                        SQL
                      )

      @courses = if @is_stride_admin
                   @courses
      elsif @is_account_admin
                   @courses.by_account_id(account_id)
      elsif @is_teacher
                   @courses.where(canvas_id: teaching_course_ids)
      else
                   @courses.enabled_accounts
      end

      canvas_course_ids = @courses.map(&:canvas_id)
      organization_name = current_organization.name

      @attendance_to_date_data[organization_name] = Attendance.where(user: @user, canvas_course_id: canvas_course_ids)
                                                              .group(:canvas_course_id)
                                                              .sum(:time_in_minutes)

      @attendance_for_date_data[organization_name] = Attendance.where(attendance_date: params[:date].to_date,
                                                                      user: @user,
                                                                      canvas_course_id: canvas_course_ids)
                                                               .group(:canvas_course_id)
                                                               .pluck(:canvas_course_id,
                                                                      'SUM(time_in_minutes) AS total_time')
                                                               .to_h
                                                               .transform_values(&:presence)
    end
  end

  # def blueprint
  #   authorize! :access, :blueprint_courses

  #   courses = Course.blueprint.by_account_id(current_account.canvas_id)

  #   @paginated_data = paginated(courses, pagination_params)
  # end

  def subject_grade_pairs
    authorize! :access, :subject_grade_pairs

    courses = Course
                .where.not(subject: [ '', nil ], grade_level: [ '', nil ])
                .select(
                  'DISTINCT ON (subject, grade_level) ' \
                  'subject, grade_level, default_time_to_complete, ' \
                  "(subject || '-' || grade_level) AS label"
                )
                .order(:subject, :grade_level)

    @paginated_data = paginated(courses, pagination_params)
  end

  # def bulk_update
  #   authorize! :access, :blueprint_courses

  #   courses_params = params.require(:courses).map do |course|
  #     course.permit(:canvas_id, :default_time_to_complete)
  #   end

  #   updates = courses_params.map do |course|
  #     { canvas_id: course[:canvas_id], default_time_to_complete: course[:default_time_to_complete] }
  #   end

  #   Course.upsert_all(updates, unique_by: :canvas_id)

  #   render json: { message: 'Courses updated successfully' }, status: :ok
  # rescue StandardError => e
  #   Rails.logger.error "Error: #{e.message}"
  #   Rails.logger.error e.backtrace
  #   render json: { error: e.message }, status: :unprocessable_entity
  # end

  def bulk_update_pairs
    authorize! :access, :subject_grade_pairs

    pair_params = params.require(:pairs).map do |pair|
      pair.permit(:subject, :grade_level, :default_time_to_complete)
    end

    pair_params.each do |p_params|
      Course
        .where(subject: p_params[:subject], grade_level: p_params[:grade_level])
        .update_all(default_time_to_complete: p_params[:default_time_to_complete])
    end

    render json: { message: 'Courses updated successfully' }, status: :ok
  rescue StandardError => e
    Rails.logger.error "Error: #{e.message}"
    Rails.logger.error e.backtrace
    render json: { error: e.message }, status: :unprocessable_entity
  end

  private

  def set_user
    @user = if current_ability.user_is_stride_admin? || current_ability.user_is_school_admin?
              User.find_by_canvas_id!(params[:user_id])
    elsif current_ability.user_is_teacher?
              User.joins(:enrollments)
                  .where(enrollments: { workflow_state: %w[active concluded],
                                        base_role_type: 'StudentEnrollment',
                                        canvas_course_id: current_user.courses.pluck(:canvas_id) })
                  .find_by_canvas_id!(params[:user_id])
    elsif current_ability.user_is_observer?
              if params[:organization_name]
                PandaPal::Organization.find_by_name!(params[:organization_name]).switch_tenant do
                  User.find_by_canvas_id!(params[:user_id])
                end
              else
                current_user.enrollment_observed_users.find_by_canvas_id!(params[:user_id])
              end
    end
  end
end
