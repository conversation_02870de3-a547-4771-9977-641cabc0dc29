# frozen_string_literal: true

module Pagination
  extend ActiveSupport::Concern

  def paginated(collection, params = {})
    params = params.merge(count: collection.size)
    PaginatedData.new(collection, params)
  end

  class PaginatedData
    PER_PAGE = 50
    PAGE = 1

    def initialize(collection, params = {})
      @collection = collection
      @params     = params
    end

    def result
      raise 'Illegal Pagination Params' if invalid_pagination_params?

      @collection.limit(per_page).offset(offset)
    end

    def total_pages
      return 0 if count.zero?

      (count / per_page.to_f).ceil
    end

    def count
      @params[:count].to_i
    end

    def per_page
      (@params[:per_page]&.strip.presence || PER_PAGE).to_i
    end

    def page
      (@params[:page]&.strip.presence || PAGE).to_i
    end

    private

    def offset
      per_page * (page - 1)
    end

    def invalid_pagination_params?
      per_page < 1 || page < 1
    end
  end
end
