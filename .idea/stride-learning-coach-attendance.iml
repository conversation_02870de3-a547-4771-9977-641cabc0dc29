<?xml version="1.0" encoding="UTF-8"?>
<module type="RUBY_MODULE" version="4">
  <component name="FacetManager">
    <facet type="RailsFacetType" name="Ruby on Rails">
      <configuration>
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_SUPPORT_REMOVED" VALUE="false" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_TESTS_SOURCES_PATCHED" VALUE="true" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_APPLICATION_ROOT" VALUE="$MODULE_DIR$" />
      </configuration>
    </facet>
  </component>
  <component name="ModuleRunConfigurationManager">
    <shared />
  </component>
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/features" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/test" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/.bundle" />
      <excludeFolder url="file://$MODULE_DIR$/components" />
      <excludeFolder url="file://$MODULE_DIR$/log" />
      <excludeFolder url="file://$MODULE_DIR$/public/packs" />
      <excludeFolder url="file://$MODULE_DIR$/public/system" />
      <excludeFolder url="file://$MODULE_DIR$/tmp" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/cache" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="RModuleSettingsStorage">
    <LOAD_PATH number="0" />
    <I18N_FOLDERS number="1" string0="$MODULE_DIR$/config/locales" />
  </component>
  <component name="RailsGeneratorsCache">
    <option name="generators">
      <list>
        <option value="active_record:migration" />
        <option value="active_record:model" />
        <option value="active_record:observer" />
        <option value="active_record:session_migration" />
        <option value="controller" />
        <option value="erb:controller" />
        <option value="erb:mailer" />
        <option value="erb:scaffold" />
        <option value="generator" />
        <option value="helper" />
        <option value="integration_test" />
        <option value="mailer" />
        <option value="metal" />
        <option value="migration" />
        <option value="model" />
        <option value="model_subclass" />
        <option value="observer" />
        <option value="performance_test" />
        <option value="plugin" />
        <option value="resource" />
        <option value="scaffold" />
        <option value="scaffold_controller" />
        <option value="session_migration" />
        <option value="stylesheets" />
        <option value="test_unit:controller" />
        <option value="test_unit:helper" />
        <option value="test_unit:integration" />
        <option value="test_unit:mailer" />
        <option value="test_unit:model" />
        <option value="test_unit:observer" />
        <option value="test_unit:performance" />
        <option value="test_unit:plugin" />
        <option value="test_unit:scaffold" />
      </list>
    </option>
    <option name="myGenerators">
      <list>
        <option value="active_record:migration" />
        <option value="active_record:model" />
        <option value="active_record:observer" />
        <option value="active_record:session_migration" />
        <option value="controller" />
        <option value="erb:controller" />
        <option value="erb:mailer" />
        <option value="erb:scaffold" />
        <option value="generator" />
        <option value="helper" />
        <option value="integration_test" />
        <option value="mailer" />
        <option value="metal" />
        <option value="migration" />
        <option value="model" />
        <option value="model_subclass" />
        <option value="observer" />
        <option value="performance_test" />
        <option value="plugin" />
        <option value="resource" />
        <option value="scaffold" />
        <option value="scaffold_controller" />
        <option value="session_migration" />
        <option value="stylesheets" />
        <option value="test_unit:controller" />
        <option value="test_unit:helper" />
        <option value="test_unit:integration" />
        <option value="test_unit:mailer" />
        <option value="test_unit:model" />
        <option value="test_unit:observer" />
        <option value="test_unit:performance" />
        <option value="test_unit:plugin" />
        <option value="test_unit:scaffold" />
      </list>
    </option>
  </component>
  <component name="RailsPaths" isManagedAutomatically="true">
    <entry key="app">
      <value>file://$MODULE_DIR$/app</value>
    </entry>
    <entry key="app/assets">
      <value>file://$MODULE_DIR$/app/assets</value>
    </entry>
    <entry key="app/channels">
      <value>file://$MODULE_DIR$/app/channels</value>
    </entry>
    <entry key="app/controllers">
      <value>file://$MODULE_DIR$/app/controllers</value>
    </entry>
    <entry key="app/helpers">
      <value>file://$MODULE_DIR$/app/helpers</value>
    </entry>
    <entry key="app/mailers">
      <value>file://$MODULE_DIR$/app/mailers</value>
    </entry>
    <entry key="app/models">
      <value>file://$MODULE_DIR$/app/models</value>
    </entry>
    <entry key="app/views">
      <value>file://$MODULE_DIR$/app/views</value>
    </entry>
    <entry key="config">
      <value>file://$MODULE_DIR$/config</value>
    </entry>
    <entry key="config/cable">
      <value>file://$MODULE_DIR$/config/cable.yml</value>
    </entry>
    <entry key="config/database">
      <value>file://$MODULE_DIR$/config/database.yml</value>
    </entry>
    <entry key="config/environment">
      <value>file://$MODULE_DIR$/config/environment.rb</value>
    </entry>
    <entry key="config/environments">
      <value>file://$MODULE_DIR$/config/environments</value>
    </entry>
    <entry key="config/initializers">
      <value>file://$MODULE_DIR$/config/initializers</value>
    </entry>
    <entry key="config/locales">
      <value>file://$MODULE_DIR$/config/locales</value>
    </entry>
    <entry key="config/routes">
      <value>file://$MODULE_DIR$/config/routes</value>
    </entry>
    <entry key="config/routes.rb">
      <value>file://$MODULE_DIR$/config/routes.rb</value>
    </entry>
    <entry key="config/secrets">
      <value>file://$MODULE_DIR$/config</value>
    </entry>
    <entry key="db">
      <value>file://$MODULE_DIR$/db</value>
    </entry>
    <entry key="db/migrate">
      <value>file://$MODULE_DIR$/db/migrate</value>
    </entry>
    <entry key="db/seeds.rb">
      <value>file://$MODULE_DIR$/db/seeds.rb</value>
    </entry>
    <entry key="lib">
      <value>file://$MODULE_DIR$/lib</value>
    </entry>
    <entry key="lib/assets">
      <value>file://$MODULE_DIR$/lib/assets</value>
    </entry>
    <entry key="lib/tasks">
      <value>file://$MODULE_DIR$/lib/tasks</value>
    </entry>
    <entry key="lib/templates">
      <value>file://$MODULE_DIR$/lib/templates</value>
    </entry>
    <entry key="log">
      <value>file://$MODULE_DIR$/log/development.log</value>
    </entry>
    <entry key="public">
      <value>file://$MODULE_DIR$/public</value>
    </entry>
    <entry key="public/javascripts">
      <value>file://$MODULE_DIR$/public/javascripts</value>
    </entry>
    <entry key="public/stylesheets">
      <value>file://$MODULE_DIR$/public/stylesheets</value>
    </entry>
    <entry key="tmp">
      <value>file://$MODULE_DIR$/tmp</value>
    </entry>
    <entry key="vendor">
      <value>file://$MODULE_DIR$/vendor</value>
    </entry>
    <entry key="vendor/assets">
      <value>file://$MODULE_DIR$/vendor/assets</value>
    </entry>
  </component>
</module>