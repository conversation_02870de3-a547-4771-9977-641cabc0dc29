// See the shakacode/shakapacker README and docs directory for advice on customizing your webpackConfig.
const { generateWebpackConfig } = require('shakapacker')
const path = require('path')

const webpackConfig = generateWebpackConfig()

webpackConfig.resolve.alias = {
  ...webpackConfig.resolve.alias,
  '@toolkit': path.resolve(__dirname, '../../node_modules/@inst_proserv/toolkit/esm')
}

module.exports = webpackConfig
