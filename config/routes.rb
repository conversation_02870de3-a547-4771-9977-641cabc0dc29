require 'sidekiq/web'
require 'sidekiq-scheduler/web'

Rails.application.routes.draw do
  if Rails.env.production?
    Sidekiq::Web.use(Rack::Auth::Basic) do |username, password|
      username == ENV['SIDEKIQ_USERNAME'] && password == ENV['SIDEKIQ_PASSWORD']
    end
  end
  mount Sidekiq::Web => '/sidekiq'

  root to: 'panda_pal/lti#launch'
  mount PandaPal::Engine, at: '/lti'

  scope '/organizations/:organization_id' do
    lti_nav account_navigation: 'lti#account_navigation'
    lti_nav course_navigation: 'lti#course_navigation'
    lti_nav user_navigation: 'lti#user_navigation'

    namespace :api, defaults: { format: :json } do
      namespace :v1 do
        resources :accounts, only: [ :show, :update ]

        resources :reports, only: [ :create, :show ] do
          get :latest_completed_report, on: :collection
        end

        resources :users, only: [ :index, :show ] do
          get 'search', on: :collection
          post 'attendance/record'

          resources :courses, only: [ :index ]

          collection do
            get :observed_users
          end
        end

        resources :imports, only: [ :create, :show ] do
          get :latest_import, on: :collection
        end

        # get 'courses/blueprint'
        get 'courses/subject_grade_pairs'
        # post 'courses/bulk_update'
        post 'courses/bulk_update_pairs'

        get 'attendance/missing'

        post ':student_id/attendance' => 'attendance#attendance', as: :post_attendance
      end
    end
  end
end
