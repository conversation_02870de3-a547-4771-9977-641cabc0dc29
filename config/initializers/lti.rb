# frozen_string_literal: true

PandaPal.lti_options = {
                          title: 'Learning Coach Attendance',
                          lti_spec_version: 'v1p3',
                          settings_structure: {
                            canvas: {
                              type: 'Hash',
                              required: true,
                              properties: {
                                base_url: { type: 'String', required: true },
                                api_token: { type: 'String', required: true },
                                default_time_zone: { type: 'String' },
                                external_tool_id: { type: 'Integer' }
                              }
                            },
                            # ------------------------------------------
                            # Data Shipper Destinations consumed by `inst_data_shipper` gem for Hosted Data.
                            # This is an optional array specifying destinations/url where data will be sent.
                            # Each element in the array represents a different service that should receive data.
                            # For eg. ['hosted-data://<EMAIL>']
                            # ------------------------------------------
                            data_shipper_destinations: {
                              type: 'Array',
                              required: false,
                              description: 'An array of destinations to send data to.',
                              item: { type: 'String' }
                            },
                            # TODO: Remove Legacy SFTP Importer
                            sftp: {
                              type: 'Hash',
                              required: false,
                              properties: {
                                file_path: { type: 'String', required: false },
                                host: { type: 'String', required: false },
                                user: { type: 'String', required: false },
                                key: { type: 'String', required: false }
                              }
                            },
                            sftp_endpoints: {
                              type: 'Array',
                              required: false,
                              description: 'An array of sftp endpoints to import data from.',
                              item: {
                                type: 'Hash',
                                properties: {
                                  file_path: { type: 'String', required: false },
                                  host: { type: 'String', required: false },
                                  user: { type: 'String', required: false },
                                  password: { type: 'String', required: false }
                                }
                              }
                            },
                            instance_names: { type: 'Array', required: false }
                          }
                        }

# Environments reflect those used by Canvas
PandaPal.lti_environments = {
  domain: ENV.fetch('PROD_DOMAIN', nil),
  beta_domain: ENV.fetch('BETA_DOMAIN', nil),
  test_domain: ENV.fetch('TEST_DOMAIN', nil)
}

PandaPal.lti_custom_params = {
  custom_canvas_user_id: '$Canvas.user.id',
  custom_canvas_role: '$Canvas.membership.roles',
  custom_course_id: '$Canvas.course.id',
  custom_canvas_account_id: '$Canvas.account.id',
  custom_canvas_brand_config: '$com.instructure.brandConfigJSON'
}

PandaPal.stage_navigation(:account_navigation, {
                            enabled: true,
                            # url: :account_index, # Optional if using lti_nav
                            text: 'Learning Coach Attendance',
                            visibility: 'admins'
                          })

PandaPal.stage_navigation(:course_navigation, {
                            enabled: true,
                            text: 'Learning Coach Attendance'
                          })

PandaPal.stage_navigation(:user_navigation, {
                            enabled: true,
                            text: 'Learning Coach Attendance'
                          })
