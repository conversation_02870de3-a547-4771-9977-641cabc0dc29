SecureHeaders::Configuration.default do |config|
  PandaPal::SecureHeaders.apply_defaults(config)

  config.csp[:style_src] << 'https://fonts.googleapis.com/css'
  config.csp[:font_src] << 'https://fonts.gstatic.com'

  # React Devtools
  config.csp[:script_src] ||= %w[]
  config.csp[:script_src] << "'unsafe-inline'"
  config.csp[:script_src] << "'unsafe-eval'"

  config.csp[:connect_src] ||= []
  config.csp[:connect_src] |= %w['self' https://*.insops.net wss:]
  config.csp[:connect_src] |= %w[https://*.tableau.com]

  config.csp[:object_src] ||= []
  config.csp[:object_src] |= %w['self' blob:]

  config.csp[:img_src] ||= []
  config.csp[:img_src] |= %w['self' blob: data: *]
end
