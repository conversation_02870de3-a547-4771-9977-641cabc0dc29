# #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#


class CreateAdmins < ActiveRecord::Migration[5.1]
  def change
    create_table :admins do |t|
      t.bigint :canvas_id, null: false
      t.string :role_name
      t.bigint :canvas_account_id
      t.bigint :canvas_role_id
      t.bigint :canvas_user_id
      t.string :workflow_state

      t.timestamps
    end
    add_index :admins, :canvas_id, unique: true
    add_index :admins, :canvas_role_id
    add_index :admins, :canvas_user_id
    add_index :admins, :canvas_account_id
  end
end
