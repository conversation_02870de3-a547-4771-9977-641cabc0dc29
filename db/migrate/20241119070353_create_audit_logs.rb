class CreateAuditLogs < ActiveRecord::Migration[7.0]
  def change
    create_table :audit_logs do |t|
      t.bigint :student_canvas_id
      t.string :student_sis_id
      t.string :student_name
      t.string :course_sis_id
      t.bigint :course_canvas_id
      t.string :course_name
      t.date :attendance_date
      t.integer :previous_value
      t.integer :updated_value
      t.string :actioning_user_sis_id
      t.bigint :actioning_user_canvas_id
      t.string :actioning_user_name

      t.timestamps
    end
  end
end
