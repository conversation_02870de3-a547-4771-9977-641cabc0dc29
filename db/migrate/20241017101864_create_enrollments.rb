# #
# AUTO GENERATED MIGRATION
# This migration was auto generated by the CanvasSync Gem.
# You can add new columns to this table, but removing or
# re-naming ones created here may break Canvas Syncing.
#


class CreateEnrollments < ActiveRecord::Migration[5.1]
  def change
    create_table :enrollments do |t|
      t.bigint :canvas_id, null: false
      t.bigint :canvas_course_id
      t.string :course_sis_id
      t.bigint :canvas_user_id
      t.string :user_sis_id
      t.string :role
      t.integer :canvas_role_id
      t.bigint :canvas_section_id
      t.string :workflow_state
      t.string :base_role_type

      t.timestamps
    end

    add_index :enrollments, :canvas_id, unique: true
    add_index :enrollments, :canvas_course_id
    add_index :enrollments, :canvas_user_id
  end
end
