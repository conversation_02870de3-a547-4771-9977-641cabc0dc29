class ChangeCanvasUserIdToBigintInTables < ActiveRecord::Migration[7.0]
  def change
    reversible do |dir|
      dir.up do
        change_column :user_shard_associations, :canvas_user_id, :bigint
        change_column :api_keys, :canvas_user_id, :bigint
      end

      dir.down do
        change_column :user_shard_associations, :canvas_user_id, :integer
        change_column :api_keys, :canvas_user_id, :integer
      end
    end
  end
end
