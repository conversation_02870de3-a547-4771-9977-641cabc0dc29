version: '2'

services:
  web:
    image: "${TEST_IMAGE_NAME}"
    environment:
      APP_DOMAIN: web
      GERGICH_KEY: "${GERGICH_KEY}"
      GERRIT_PROJECT: "${GERRIT_PROJECT}"
      GERRIT_HOST: "${GERRIT_HOST}"
      GERRIT_BRANCH: "${GERRIT_BRANCH}"
      DATABASE_USERNAME: postgres
      DATABASE_PASSWORD: password
      DATABASE_HOST: postgres
      NODE_ENV: test
      RAILS_ENV: test
      REDIS_URL: redis://redis:6379
      HMAC_SECRET_KEY: hmac-secret-key
    links:
      - postgres
      - redis
    volumes:
      - '.git:/usr/src/app/.git'

  redis:
    image: redis
    container_name: cache
    expose:
      - 6379

  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust

volumes:
  coverage: {}
